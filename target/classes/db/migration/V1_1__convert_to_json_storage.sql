-- Migration to convert transaction storage from relational to JSON document style
-- This migration adds JSON columns to the transaction table and migrates existing data

-- Step 1: Add new JSON columns to transaction table
ALTER TABLE transaction 
ADD COLUMN line_items_json TEXT,
ADD COLUMN tenders_json TEXT;

-- Step 2: Migrate existing line items to JSON format
UPDATE transaction 
SET line_items_json = (
    SELECT COALESCE(
        JSON_ARRAYAGG(
            JSON_OBJECT(
                'line_number', tli.line_number,
                'item_id', tli.item_id,
                'item_description', i.description,
                'upc', i.upc,
                'quantity', tli.quantity,
                'unit_price', tli.unit_price,
                'extended_price', tli.extended_price,
                'discount_amount', COALESCE(tli.discount_amount, 0),
                'tax_amount', COALESCE(tli.tax_amount, 0),
                'tax_rate', COALESCE(i.tax_rate, 0),
                'is_voided', COALESCE(tli.is_voided, false),
                'void_reason', tli.void_reason,
                'weight', tli.weight,
                'line_item_type', 'SALE',
                'serial_number', tli.serial_number,
                'sequence_number', tli.sequence_number,
                'is_taxable', COALESCE(i.is_taxable, true),
                'is_food_stampable', COALESCE(i.is_food_stampable, false),
                'is_wic_eligible', COALESCE(i.is_wic_eligible, false)
            )
        ),
        '[]'
    )
    FROM transactionlineitem tli
    LEFT JOIN item i ON tli.item_id = i.item_id
    WHERE tli.transaction_id = transaction.transaction_id
);

-- Step 3: Migrate existing tenders to JSON format
UPDATE transaction 
SET tenders_json = (
    SELECT COALESCE(
        JSON_ARRAYAGG(
            JSON_OBJECT(
                'tender_type', tt.tender_type,
                'amount', tt.amount,
                'card_last_four', tt.card_last_four,
                'authorization_code', tt.authorization_code,
                'reference_number', tt.reference_number,
                'approval_code', tt.approval_code,
                'currency', COALESCE(tt.currency, 'USD'),
                'tender_sequence', ROW_NUMBER() OVER (ORDER BY tt.transaction_tender_id)
            )
        ),
        '[]'
    )
    FROM transactiontender tt
    WHERE tt.transaction_id = transaction.transaction_id
);

-- Step 4: Set empty JSON arrays for transactions with no line items or tenders
UPDATE transaction 
SET line_items_json = '[]' 
WHERE line_items_json IS NULL;

UPDATE transaction 
SET tenders_json = '[]' 
WHERE tenders_json IS NULL;

-- Step 5: Drop foreign key constraints first
ALTER TABLE transactionlineitem DROP CONSTRAINT IF EXISTS transactionlineitem_transaction_id_fkey;
ALTER TABLE transactionlineitem DROP CONSTRAINT IF EXISTS transactionlineitem_item_id_fkey;
ALTER TABLE transactiontender DROP CONSTRAINT IF EXISTS transactiontender_transaction_id_fkey;

-- Step 6: Drop the old tables (commented out for safety - uncomment when ready)
-- DROP TABLE IF EXISTS transactionlineitemtax CASCADE;
-- DROP TABLE IF EXISTS transactionlineitem CASCADE;
-- DROP TABLE IF EXISTS transactiontender CASCADE;

-- Step 7: Create indexes on the new JSON columns for better query performance
CREATE INDEX IF NOT EXISTS idx_transaction_line_items_json ON transaction USING GIN (line_items_json);
CREATE INDEX IF NOT EXISTS idx_transaction_tenders_json ON transaction USING GIN (tenders_json);

-- Add a comment to indicate the migration has completed successfully
COMMENT ON COLUMN transaction.line_items_json IS 'JSON array of transaction line items';
COMMENT ON COLUMN transaction.tenders_json IS 'JSON array of transaction tenders';
