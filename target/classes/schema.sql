DROP TABLE IF EXISTS transaction_line_items;
DROP TABLE IF EXISTS transaction;
DROP TABLE IF EXISTS itemprice;
DROP TABLE IF EXISTS item;
DROP TABLE IF EXISTS department;
DROP TABLE IF EXISTS employee;
DROP TABLE IF EXISTS rolepermission;
DROP TABLE IF EXISTS permission;
DROP TABLE IF EXISTS role;
DROP TABLE IF EXISTS customer;
DROP TABLE IF EXISTS lane;
DROP TABLE IF EXISTS store;

CREATE TABLE store (
    store_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    store_name VARCHAR(255) NOT NULL,
    address VARCHAR(255),
    city VARCHAR(255),
    state VARCHAR(2),
    zip_code VA<PERSON>HAR(10),
    phone_number VARCHAR(20),
    tax_rate NUMERIC(5,4) DEFAULT 0.0,
    store_no INTEGER UNIQUE,
    wic_state_id INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE lane (
    lane_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    store_id BIGINT NOT NULL,
    lane_name VARCHAR(100) NOT NULL,
    lane_number INTEGER,
    is_active BOOLEAN DEFAULT TRUE,
    is_express BOOLEAN DEFAULT FALSE,
    max_items INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (store_id) REFERENCES store(store_id)
);

CREATE TABLE department (
    department_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    department_name VARCHAR(255) NOT NULL,
    department_description VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE item (
    item_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    description VARCHAR(255) NOT NULL,
    sku VARCHAR(50),
    upc VARCHAR(20),
    brand VARCHAR(255),
    size VARCHAR(100),
    unit_of_measure VARCHAR(10) DEFAULT 'EA',
    cost NUMERIC(10,2) DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    is_taxable BOOLEAN DEFAULT TRUE,
    is_food_stampable BOOLEAN DEFAULT FALSE,
    is_wic_eligible BOOLEAN DEFAULT FALSE,
    is_age_restricted BOOLEAN DEFAULT FALSE,
    minimum_age INTEGER DEFAULT 0,
    is_returnable BOOLEAN DEFAULT TRUE,
    is_scale_item BOOLEAN DEFAULT FALSE,
    is_department_sale BOOLEAN DEFAULT FALSE,
    tare_weight NUMERIC(8,3) DEFAULT 0,
    item_image_url VARCHAR(255),
    department_id BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (department_id) REFERENCES department(department_id)
);

CREATE TABLE itemprice (
    item_price_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    item_id BIGINT NOT NULL,
    store_id BIGINT NOT NULL,
    price NUMERIC(10,2) NOT NULL,
    start_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_date TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (item_id) REFERENCES item(item_id),
    FOREIGN KEY (store_id) REFERENCES store(store_id)
);

CREATE TABLE customer (
    customer_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    loyalty_card_number VARCHAR(50) UNIQUE,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    phone_number VARCHAR(20),
    email VARCHAR(255),
    tax_exempt_id VARCHAR(255),
    date_of_birth DATE,
    address_line1 VARCHAR(255),
    address_line2 VARCHAR(255),
    city VARCHAR(100),
    state VARCHAR(2),
    postal_code VARCHAR(10),
    country VARCHAR(50),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE role (
    role_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    role_name VARCHAR(50) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE permission (
    permission_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    permission_name VARCHAR(255) NOT NULL UNIQUE,
    permission_description VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE rolepermission (
    role_id BIGINT NOT NULL,
    permission_id BIGINT NOT NULL,
    PRIMARY KEY (role_id, permission_id),
    FOREIGN KEY (role_id) REFERENCES role(role_id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permission(permission_id) ON DELETE CASCADE
);

CREATE TABLE employee (
    employee_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    login_id VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255),
    email VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP,
    auth_id VARCHAR(36),
    role_id BIGINT,
    store_id BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (role_id) REFERENCES role(role_id),
    FOREIGN KEY (store_id) REFERENCES store(store_id)
);

CREATE TABLE transaction (
    transaction_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    store_id BIGINT NOT NULL,
    lane_id BIGINT NOT NULL,
    employee_id BIGINT,
    customer_id BIGINT,
    transaction_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    status VARCHAR(255) DEFAULT 'OPEN',
    subtotal NUMERIC(10,2) DEFAULT 0,
    tax_amount NUMERIC(10,2) DEFAULT 0,
    discount_amount NUMERIC(10,2) DEFAULT 0,
    total_amount NUMERIC(10,2) DEFAULT 0,
    item_count INTEGER DEFAULT 0,
    is_training BOOLEAN DEFAULT FALSE,
    is_voided BOOLEAN DEFAULT FALSE,
    void_reason VARCHAR(255),
    original_transaction_id BIGINT,
    transaction_data CLOB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE transaction_line_items (
    line_item_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    transaction_id BIGINT NOT NULL,
    item_id BIGINT NOT NULL,
    quantity INTEGER,
    price NUMERIC(10,2),
    discount_amount NUMERIC(10,2),
    total_amount NUMERIC(10,2),
    FOREIGN KEY (transaction_id) REFERENCES transaction(transaction_id),
    FOREIGN KEY (item_id) REFERENCES item(item_id)
);

-- Indexes
CREATE INDEX idx_lane_store_id ON lane(store_id);
CREATE INDEX idx_item_department_id ON item(department_id);
CREATE INDEX idx_item_upc ON item(upc);
CREATE INDEX idx_itemprice_item_id ON itemprice(item_id);
CREATE INDEX idx_itemprice_store_id ON itemprice(store_id);
CREATE INDEX idx_customer_loyalty_card ON customer(loyalty_card_number);
CREATE INDEX idx_employee_role_id ON employee(role_id);
CREATE INDEX idx_employee_store_id ON employee(store_id);
CREATE INDEX idx_employee_login_id ON employee(login_id);
CREATE INDEX idx_transaction_store_timestamp ON transaction(store_id, transaction_timestamp);
CREATE INDEX idx_transaction_status ON transaction(status);
CREATE INDEX idx_transaction_customer ON transaction(customer_id);
CREATE INDEX idx_transaction_employee ON transaction(employee_id);
CREATE INDEX idx_transaction_total ON transaction(total_amount);