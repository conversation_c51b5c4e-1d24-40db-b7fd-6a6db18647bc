# Staging Profile - PostgreSQL Database Configuration
spring:
  datasource:
    url: ***********************************************
    username: ${DB_USERNAME:trupos_staging}
    password: ${DB_PASSWORD:staging_password}
    driver-class-name: org.postgresql.Driver
    
    # Moderate connection pooling for staging
    hikari:
      maximum-pool-size: 10
      minimum-idle: 2
      connection-timeout: 20000
      idle-timeout: 300000
      max-lifetime: 1200000

  jpa:
    hibernate:
      ddl-auto: update  # Allow schema updates in staging for testing
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect

# Staging logging - moderate verbosity
logging:
  level:
    "[com.trupos]": DEBUG
    "[org.springframework.security]": INFO
    "[org.hibernate.SQL]": INFO
  file:
    name: logs/trupos-api-staging.log
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  logback:
    rollingpolicy:
      max-file-size: 25MB
      max-history: 60
      total-size-cap: 2GB
    
# Staging-specific settings
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,env  # More endpoints for staging testing
        
# Relaxed CORS for staging testing
cors:
  allowed-origins: 
    - http://localhost:5173
    - http://localhost:3000
    - https://staging.yourdomain.com
  allowed-methods:
    - GET
    - POST
    - PUT
    - DELETE
    - OPTIONS
    - PATCH
  allowed-headers: "*"
  allow-credentials: true
