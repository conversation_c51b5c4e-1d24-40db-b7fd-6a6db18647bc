-- Stores
INSERT INTO store (store_name, address, city, state, zip_code, phone_number, tax_rate, store_no, wic_state_id)
VALUES ('TruPOS Main Store', '123 Main St', 'Anytown', 'CA', '90210', '************', 0.0725, 1, 123456);

-- Lanes
INSERT INTO lane (store_id, lane_name, lane_number, is_active, is_express, max_items)
VALUES (1, 'Lane 1', 1, TRUE, FALSE, NULL),
       (1, 'Express Lane', 2, TRUE, TRUE, 15);

-- Departments
INSERT INTO department (department_name, department_description, is_active)
VALUES ('Grocery', 'General grocery items', TRUE),
       ('Produce', 'Fresh fruits and vegetables', TRUE),
       ('Dairy', 'Milk, cheese, and dairy products', TRUE),
       ('Meat', 'Fresh meat and poultry', TRUE);

-- Items
INSERT INTO item (description, sku, upc, brand, size, unit_of_measure, cost, department_id, is_taxable, is_food_stampable, is_wic_eligible, is_scale_item)
VALUES
('Organic Bananas', 'PROD-001', '123456789012', 'Fresh Farms', '1 lb', 'LB', 0.35, 2, FALSE, TRUE, TRUE, TRUE),
('Whole Milk', 'DAIRY-001', '223456789013', 'Dairy Fresh', '1 gallon', 'EA', 2.50, 3, FALSE, TRUE, TRUE, FALSE),
('Ground Beef 80/20', 'MEAT-001', '323456789014', 'Premium Meats', '1 lb', 'LB', 4.50, 4, FALSE, TRUE, FALSE, TRUE),
('White Bread', 'BAKERY-001', '423456789015', 'Fresh Baked', '20 oz', 'EA', 1.25, 1, FALSE, TRUE, TRUE, FALSE),
('Coca-Cola 12pk', 'GROCERY-001', '523456789016', 'Coca-Cola', '12 x 12 oz', 'EA', 3.50, 1, TRUE, FALSE, FALSE, FALSE);

-- Item Prices
INSERT INTO itemprice (item_id, store_id, price)
VALUES (1, 1, 0.69),
       (2, 1, 3.99),
       (3, 1, 5.99),
       (4, 1, 2.49),
       (5, 1, 4.99);

-- Customers
INSERT INTO customer (loyalty_card_number, first_name, last_name, phone_number, email, date_of_birth, is_active)
VALUES ('1000001', 'John', 'Doe', '************', '<EMAIL>', '1980-01-15', TRUE),
       ('1000002', 'Jane', 'Smith', '************', '<EMAIL>', '1985-05-20', TRUE);

-- Roles
INSERT INTO role (role_name)
VALUES ('Manager'),
       ('Cashier');

-- Employees (bcrypt hashed "1234" for password_hash)
INSERT INTO employee (first_name, last_name, login_id, password, role_id, store_id, is_active)
VALUES ('Store', 'Manager', '1001', '$2a$10$2sKeKcDUfIgcprmlrV4bSODbWiicqlG6GbsARB525ySFapd2CAY.G', 1, 1, TRUE), -- password = 'password'
       ('Store', 'Cashier', '2001', '$2a$10$2sKeKcDUfIgcprmlrV4bSODbWiicqlG6GbsARB525ySFapd2CAY.G', 2, 1, TRUE); -- password = 'password'