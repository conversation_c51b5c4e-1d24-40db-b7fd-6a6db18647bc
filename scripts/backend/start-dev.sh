#!/bin/bash

# TruPOS Development Startup Script (H2 Database)
echo "🚀 Starting TruPOS API in DEVELOPMENT mode with H2 database..."
echo "📊 H2 Console will be available at: http://localhost:8080/api/h2-console"
echo "📖 API Documentation: http://localhost:8080/api/swagger-ui.html"
echo "🔧 Profile: dev"
echo ""

# Set development profile
export SPRING_PROFILES_ACTIVE=dev

# Start the application
if [ -f "target/trupos-api-*.jar" ]; then
    echo "Starting from JAR file..."
    java -jar target/trupos-api-*.jar
else
    echo "Starting with Maven..."
    ./mvnw spring-boot:run -Dspring-boot.run.profiles=dev
fi
