#!/bin/bash

# TruPOS Backend Startup Script
# This script starts the TruPOS SpringBoot backend service

echo "🚀 Starting TruPOS Backend Service..."

# Get the project root directory (two levels up from scripts/development/)
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
BACKEND_DIR="$PROJECT_ROOT/backend"

echo "📍 Location: $BACKEND_DIR"
echo "📦 JAR File: trupos-api-0.0.1-SNAPSHOT.jar"
echo ""

# Change to the backend directory
cd "$BACKEND_DIR"

# Check if JAR file exists
if [ ! -f "target/trupos-api-0.0.1-SNAPSHOT.jar" ]; then
    echo "❌ Error: JAR file not found!"
    echo "   Expected: target/trupos-api-0.0.1-SNAPSHOT.jar"
    echo "   Please build the project first with: mvn clean package"
    exit 1
fi

echo "✅ JAR file found"
echo "🔄 Starting backend service..."
echo "📚 API Documentation will be available at: http://localhost:8080/swagger-ui.html"
echo ""

# Start the backend service
java -jar target/trupos-api-0.0.1-SNAPSHOT.jar

echo ""
echo "🛑 Backend service stopped"
