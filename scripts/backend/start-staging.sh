#!/bin/bash

# TruPOS Staging Startup Script (PostgreSQL Database)
echo "🎭 Starting TruPOS API in STAGING mode with PostgreSQL database..."
echo "📖 API Documentation: http://localhost:8080/api/swagger-ui.html"
echo "🔧 Profile: staging"
echo ""

# Check if PostgreSQL is running
if ! nc -z localhost 5432; then
    echo "❌ PostgreSQL is not running on localhost:5432"
    echo "💡 Start PostgreSQL with: docker-compose up postgres -d"
    exit 1
fi

# Set staging profile
export SPRING_PROFILES_ACTIVE=staging

# Set database credentials
export DB_USERNAME=${DB_USERNAME:-trupos_staging}
export DB_PASSWORD=${DB_PASSWORD:-staging_password}

echo "🔗 Connecting to PostgreSQL staging database as user: $DB_USERNAME"

# Start the application
if [ -f "target/trupos-api-*.jar" ]; then
    echo "Starting from JAR file..."
    java -jar target/trupos-api-*.jar
else
    echo "Starting with Maven..."
    ./mvnw spring-boot:run -Dspring-boot.run.profiles=staging
fi
