#!/bin/bash

# TruPOS Production Startup Script (PostgreSQL Database)
echo "🏢 Starting TruPOS API in PRODUCTION mode with PostgreSQL database..."
echo "📖 API Documentation: http://localhost:8080/api/swagger-ui.html"
echo "🔧 Profile: prod"
echo ""

# Check if PostgreSQL is running
if ! nc -z localhost 5432; then
    echo "❌ PostgreSQL is not running on localhost:5432"
    echo "💡 Start PostgreSQL with: docker-compose up postgres -d"
    echo "💡 Or install PostgreSQL locally and run the setup script"
    exit 1
fi

# Set production profile
export SPRING_PROFILES_ACTIVE=prod

# Set database credentials (override with environment variables if needed)
export DB_USERNAME=${DB_USERNAME:-trupos_user}
export DB_PASSWORD=${DB_PASSWORD:-trupos_password}

echo "🔗 Connecting to PostgreSQL as user: $DB_USERNAME"

# Start the application
if [ -f "target/trupos-api-*.jar" ]; then
    echo "Starting from JAR file..."
    java -jar target/trupos-api-*.jar
else
    echo "Starting with Maven..."
    ./mvnw spring-boot:run -Dspring-boot.run.profiles=prod
fi
