#!/bin/bash

# Normalize Employee Login IDs Script
# This script updates employee login IDs to be numeric using the API
# Replaces: update_employee_login_ids.sql

set -e  # Exit on any error

# Configuration
API_BASE_URL="${API_BASE_URL:-http://localhost:8080/api}"
VERBOSE="${VERBOSE:-false}"
DRY_RUN="${DRY_RUN:-false}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_dry_run() {
    echo -e "${YELLOW}[DRY RUN]${NC} $1"
}

# Function to check if API is available
check_api() {
    print_status "Checking API availability at $API_BASE_URL..."
    
    if curl -s -f "$API_BASE_URL/employees" > /dev/null 2>&1; then
        print_success "API is available"
        return 0
    else
        print_error "API is not available at $API_BASE_URL"
        print_error "Please ensure the backend is running and accessible"
        exit 1
    fi
}

# Function to check if a string is numeric
is_numeric() {
    [[ $1 =~ ^[0-9]+$ ]]
}

# Function to get all employees
get_employees() {
    print_status "Fetching all employees..."
    
    local response=$(curl -s -X GET "$API_BASE_URL/employees" \
        -H "Content-Type: application/json")
    
    if [ $? -eq 0 ]; then
        echo "$response"
    else
        print_error "Failed to fetch employees"
        exit 1
    fi
}

# Function to get employee details by ID
get_employee_by_id() {
    local employee_id=$1
    
    local response=$(curl -s -X GET "$API_BASE_URL/employees/$employee_id" \
        -H "Content-Type: application/json")
    
    if [ $? -eq 0 ]; then
        echo "$response"
    else
        print_error "Failed to fetch employee details for ID $employee_id"
        return 1
    fi
}

# Function to update employee login ID
update_employee_login() {
    local employee_id=$1
    local new_login_id=$2
    local employee_name=$3
    
    if [ "$DRY_RUN" = "true" ]; then
        print_dry_run "Would update employee ID $employee_id ($employee_name) login to: $new_login_id"
        return 0
    fi
    
    print_status "Updating login ID for employee ID $employee_id ($employee_name) to: $new_login_id"
    
    # Get current employee data
    local employee_data=$(get_employee_by_id "$employee_id")
    if [ $? -ne 0 ]; then
        return 1
    fi
    
    # Update the login ID in the employee data
    # Note: This is a simplified approach. In production, consider using jq for JSON manipulation
    local updated_data=$(echo "$employee_data" | sed "s/\"loginId\":\"[^\"]*\"/\"loginId\":\"$new_login_id\"/")
    
    local response=$(curl -s -w "%{http_code}" -X PUT "$API_BASE_URL/employees/$employee_id" \
        -H "Content-Type: application/json" \
        -d "$updated_data")
    
    local http_code="${response: -3}"
    local body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        print_success "Updated login ID for employee ID $employee_id ($employee_name)"
        if [ "$VERBOSE" = "true" ]; then
            echo "Response: $body"
        fi
        return 0
    else
        print_error "Failed to update login ID for employee ID $employee_id ($employee_name) - HTTP $http_code"
        if [ "$VERBOSE" = "true" ]; then
            echo "Response: $body"
        fi
        return 1
    fi
}

# Main execution
main() {
    echo "=================================================="
    echo "Employee Login ID Normalization Script"
    echo "=================================================="
    echo "API Base URL: $API_BASE_URL"
    if [ "$DRY_RUN" = "true" ]; then
        echo "Mode: DRY RUN (no changes will be made)"
    else
        echo "Mode: LIVE (changes will be applied)"
    fi
    echo "=================================================="
    
    # Check API availability
    check_api
    
    # Get all employees
    local employees_json=$(get_employees)
    
    # Parse employee data and identify non-numeric login IDs
    local total_count=0
    local non_numeric_count=0
    local success_count=0
    local failure_count=0
    
    print_status "Analyzing employee login IDs..."
    
    # Create temporary file to store employee data for processing
    local temp_file=$(mktemp)
    echo "$employees_json" > "$temp_file"
    
    # Extract employee information
    while IFS= read -r line; do
        if [[ $line =~ \"employeeId\":([0-9]+) ]]; then
            local employee_id="${BASH_REMATCH[1]}"
            ((total_count++))
            
            # Extract login ID and name for this employee
            local employee_section=$(grep -A 10 -B 2 "\"employeeId\":$employee_id" "$temp_file")
            
            if [[ $employee_section =~ \"loginId\":\"([^\"]+)\" ]]; then
                local login_id="${BASH_REMATCH[1]}"
                
                # Extract first and last name
                local first_name=""
                local last_name=""
                if [[ $employee_section =~ \"firstName\":\"([^\"]+)\" ]]; then
                    first_name="${BASH_REMATCH[1]}"
                fi
                if [[ $employee_section =~ \"lastName\":\"([^\"]+)\" ]]; then
                    last_name="${BASH_REMATCH[1]}"
                fi
                local employee_name="$first_name $last_name"
                
                if ! is_numeric "$login_id"; then
                    ((non_numeric_count++))
                    print_warning "Employee ID $employee_id ($employee_name) has non-numeric login: '$login_id'"
                    
                    # Update to use employee ID as login ID
                    if update_employee_login "$employee_id" "$employee_id" "$employee_name"; then
                        ((success_count++))
                    else
                        ((failure_count++))
                    fi
                else
                    if [ "$VERBOSE" = "true" ]; then
                        print_status "Employee ID $employee_id ($employee_name) already has numeric login: '$login_id'"
                    fi
                fi
            fi
        fi
    done < <(echo "$employees_json" | grep -o '"employeeId":[0-9]*')
    
    # Clean up
    rm -f "$temp_file"
    
    echo "=================================================="
    echo "Login ID Normalization Summary:"
    echo "Total Employees: $total_count"
    echo "Non-numeric Login IDs Found: $non_numeric_count"
    if [ "$DRY_RUN" = "false" ]; then
        echo "Successful Updates: $success_count"
        echo "Failed Updates: $failure_count"
    fi
    echo "=================================================="
    
    if [ "$DRY_RUN" = "true" ]; then
        if [ $non_numeric_count -gt 0 ]; then
            print_warning "Run without --dry-run to apply changes"
        else
            print_success "All employee login IDs are already numeric!"
        fi
        exit 0
    elif [ $failure_count -eq 0 ]; then
        if [ $non_numeric_count -eq 0 ]; then
            print_success "All employee login IDs were already numeric!"
        else
            print_success "All non-numeric login IDs have been normalized successfully!"
        fi
        exit 0
    else
        print_warning "Some login ID updates failed. Check the output above for details."
        exit 1
    fi
}

# Help function
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Normalize employee login IDs to be numeric using the TruPOS API"
    echo "Non-numeric login IDs will be replaced with the employee's ID number"
    echo ""
    echo "Options:"
    echo "  -h, --help              Show this help message"
    echo "  -u, --url URL           API base URL (default: http://localhost:8080/api)"
    echo "  -v, --verbose           Enable verbose output"
    echo "  -d, --dry-run           Show what would be changed without making changes"
    echo ""
    echo "Environment Variables:"
    echo "  API_BASE_URL           API base URL"
    echo "  VERBOSE               Enable verbose output (true/false)"
    echo "  DRY_RUN               Enable dry run mode (true/false)"
    echo ""
    echo "Examples:"
    echo "  $0 --dry-run                         # Preview changes"
    echo "  $0                                   # Apply changes"
    echo "  $0 -u http://localhost:8080/api      # Custom API URL"
    echo "  $0 -v                                # Verbose output"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -u|--url)
            API_BASE_URL="$2"
            shift 2
            ;;
        -v|--verbose)
            VERBOSE="true"
            shift
            ;;
        -d|--dry-run)
            DRY_RUN="true"
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Run main function
main
