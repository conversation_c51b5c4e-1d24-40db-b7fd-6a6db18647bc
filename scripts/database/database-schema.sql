-- TruPOS Backend Database Schema
-- This script creates the database schema for the TruPOS Spring Boot backend

-- Drop tables if they exist (in reverse order of creation to handle dependencies)
DROP TABLE IF EXISTS transaction_line_items CASCADE;
DROP TABLE IF EXISTS transaction CASCADE;
DROP TABLE IF EXISTS itemprice CASCADE;
DROP TABLE IF EXISTS item CASCADE;
DROP TABLE IF EXISTS department CASCADE;
DROP TABLE IF EXISTS employee CASCADE;
DROP TABLE IF EXISTS rolepermission CASCADE;
DROP TABLE IF EXISTS permission CASCADE;
DROP TABLE IF EXISTS role CASCADE;
DROP TABLE IF EXISTS customer CASCADE;
DROP TABLE IF EXISTS lane CASCADE;
DROP TABLE IF EXISTS store CASCADE;

-- Create store table
CREATE TABLE store (
    store_id BIGSERIAL PRIMARY KEY,
    store_name VARCHAR(255) NOT NULL,
    address VARCHAR(255),
    city VARCHAR(255),
    state VARCHAR(2),
    zip_code VARCHAR(10),
    phone_number VARCHAR(20),
    tax_rate NUMERIC(5, 4) DEFAULT 0.0,
    store_no INTEGER UNIQUE,
    wic_state_id INTEGER,
    created_at TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP
);

-- Create lane table
CREATE TABLE lane (
    lane_id BIGSERIAL PRIMARY KEY,
    store_id BIGINT NOT NULL REFERENCES store(store_id) ON DELETE CASCADE,
    lane_name VARCHAR(100) NOT NULL,
    lane_number INTEGER,
    is_active BOOLEAN DEFAULT TRUE,
    is_express BOOLEAN DEFAULT FALSE,
    max_items INTEGER,
    created_at TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP
);

-- Create department table
CREATE TABLE department (
    department_id BIGSERIAL PRIMARY KEY,
    department_name VARCHAR(255) NOT NULL,
    department_description VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP
);

-- Create item table
CREATE TABLE item (
    item_id BIGSERIAL PRIMARY KEY,
    description VARCHAR(255) NOT NULL,
    sku VARCHAR(50),
    upc VARCHAR(20),
    brand VARCHAR(255),
    size VARCHAR(100),
    unit_of_measure VARCHAR(10) DEFAULT 'EA',
    cost NUMERIC(10, 2) DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    is_taxable BOOLEAN DEFAULT TRUE,
    is_food_stampable BOOLEAN DEFAULT FALSE,
    is_wic_eligible BOOLEAN DEFAULT FALSE,
    is_age_restricted BOOLEAN DEFAULT FALSE,
    minimum_age INTEGER DEFAULT 0,
    is_returnable BOOLEAN DEFAULT TRUE,
    is_scale_item BOOLEAN DEFAULT FALSE,
    is_department_sale BOOLEAN DEFAULT FALSE,
    tare_weight NUMERIC(8, 3) DEFAULT 0,
    item_image_url VARCHAR(255),
    department_id BIGINT REFERENCES department(department_id),
    created_at TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP
);

-- Create itemprice table
CREATE TABLE itemprice (
    item_price_id BIGSERIAL PRIMARY KEY,
    item_id BIGINT NOT NULL REFERENCES item(item_id) ON DELETE CASCADE,
    store_id BIGINT NOT NULL REFERENCES store(store_id) ON DELETE CASCADE,
    price NUMERIC(10, 2) NOT NULL,
    start_date TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    end_date TIMESTAMP(6),
    created_at TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP
);

-- Create customer table
CREATE TABLE customer (
    customer_id BIGSERIAL PRIMARY KEY,
    loyalty_card_number VARCHAR(50) UNIQUE,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    phone_number VARCHAR(20),
    email VARCHAR(255),
    tax_exempt_id VARCHAR(255),
    date_of_birth DATE,
    address_line1 VARCHAR(255),
    address_line2 VARCHAR(255),
    city VARCHAR(100),
    state VARCHAR(2),
    postal_code VARCHAR(10),
    country VARCHAR(50),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP
);

-- Create role table
CREATE TABLE role (
    role_id BIGSERIAL PRIMARY KEY,
    role_name VARCHAR(50) NOT NULL UNIQUE,
    created_at TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP
);

-- Create permission table
CREATE TABLE permission (
    permission_id BIGSERIAL PRIMARY KEY,
    permission_name VARCHAR(255) NOT NULL UNIQUE,
    permission_description VARCHAR(255),
    created_at TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP
);

-- Create rolepermission table
CREATE TABLE rolepermission (
    role_id BIGINT NOT NULL REFERENCES role(role_id) ON DELETE CASCADE,
    permission_id BIGINT NOT NULL REFERENCES permission(permission_id) ON DELETE CASCADE,
    PRIMARY KEY (role_id, permission_id)
);

-- Create employee table
CREATE TABLE employee (
    employee_id BIGSERIAL PRIMARY KEY,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    login_id VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255),
    email VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP(6),
    auth_id UUID,
    role_id BIGINT REFERENCES role(role_id),
    store_id BIGINT REFERENCES store(store_id),
    created_at TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP
);

-- Create transaction table
CREATE TABLE transaction (
    transaction_id BIGSERIAL PRIMARY KEY,
    store_id BIGINT NOT NULL,
    lane_id BIGINT NOT NULL,
    employee_id BIGINT,
    customer_id BIGINT,
    transaction_timestamp TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(255) CHECK (status IN ('OPEN','COMPLETED','SUSPENDED','VOIDED','CANCELLED')) DEFAULT 'OPEN',
    subtotal NUMERIC(10, 2) DEFAULT 0,
    tax_amount NUMERIC(10, 2) DEFAULT 0,
    discount_amount NUMERIC(10, 2) DEFAULT 0,
    total_amount NUMERIC(10, 2) DEFAULT 0,
    item_count INTEGER DEFAULT 0,
    is_training BOOLEAN DEFAULT FALSE,
    is_voided BOOLEAN DEFAULT FALSE,
    void_reason VARCHAR(255),
    original_transaction_id BIGINT,
    transaction_data TEXT,
    created_at TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for performance optimization
CREATE INDEX idx_lane_store_id ON lane(store_id);
CREATE INDEX idx_item_department_id ON item(department_id);
CREATE INDEX idx_item_upc ON item(upc);
CREATE INDEX idx_itemprice_item_id ON itemprice(item_id);
CREATE INDEX idx_itemprice_store_id ON itemprice(store_id);
CREATE INDEX idx_customer_loyalty_card ON customer(loyalty_card_number);
CREATE INDEX idx_employee_role_id ON employee(role_id);
CREATE INDEX idx_employee_store_id ON employee(store_id);
CREATE INDEX idx_employee_login_id ON employee(login_id);
CREATE INDEX idx_transaction_store_timestamp ON transaction(store_id, transaction_timestamp);
CREATE INDEX idx_transaction_status ON transaction(status);
CREATE INDEX idx_transaction_customer ON transaction(customer_id);
CREATE INDEX idx_transaction_employee ON transaction(employee_id);
CREATE INDEX idx_transaction_total ON transaction(total_amount);

-- Insert sample data for testing

-- Insert sample stores
INSERT INTO store (store_name, address, city, state, zip_code, phone_number, tax_rate, store_no, wic_state_id)
VALUES
('TruPOS Main Store', '123 Main St', 'Anytown', 'CA', '90210', '************', 0.0725, 1, 123456);

-- Insert sample lanes
INSERT INTO lane (store_id, lane_name, lane_number, is_active, is_express, max_items)
VALUES
(1, 'Lane 1', 1, TRUE, FALSE, NULL),
(1, 'Express Lane', 2, TRUE, TRUE, 15);

-- Insert sample departments
INSERT INTO department (department_name, department_description, is_active)
VALUES
('Grocery', 'General grocery items', TRUE),
('Produce', 'Fresh fruits and vegetables', TRUE),
('Dairy', 'Milk, cheese, and dairy products', TRUE),
('Meat', 'Fresh meat and poultry', TRUE);

-- Insert sample items
INSERT INTO item (description, sku, upc, brand, size, unit_of_measure, cost, department_id, is_taxable, is_food_stampable, is_wic_eligible, is_scale_item)
VALUES
('Organic Bananas', 'PROD-001', '123456789012', 'Fresh Farms', '1 lb', 'LB', 0.35, 2, FALSE, TRUE, TRUE, TRUE),
('Whole Milk', 'DAIRY-001', '223456789013', 'Dairy Fresh', '1 gallon', 'EA', 2.50, 3, FALSE, TRUE, TRUE, FALSE),
('Ground Beef 80/20', 'MEAT-001', '323456789014', 'Premium Meats', '1 lb', 'LB', 4.50, 4, FALSE, TRUE, FALSE, TRUE),
('White Bread', 'BAKERY-001', '423456789015', 'Fresh Baked', '20 oz', 'EA', 1.25, 1, FALSE, TRUE, TRUE, FALSE),
('Coca-Cola 12pk', 'GROCERY-001', '523456789016', 'Coca-Cola', '12 x 12 oz', 'EA', 3.50, 1, TRUE, FALSE, FALSE, FALSE);

-- Insert sample item prices
INSERT INTO itemprice (item_id, store_id, price)
VALUES
(1, 1, 0.69),
(2, 1, 3.99),
(3, 1, 5.99),
(4, 1, 2.49),
(5, 1, 4.99);

-- Insert sample customers
INSERT INTO customer (loyalty_card_number, first_name, last_name, phone_number, email, date_of_birth, is_active)
VALUES
('1000001', 'John', 'Doe', '************', '<EMAIL>', '1980-01-15', TRUE),
('1000002', 'Jane', 'Smith', '************', '<EMAIL>', '1985-05-20', TRUE);

-- Insert sample roles
INSERT INTO role (role_name)
VALUES
('Manager'),
('Cashier');

-- Insert sample employees
INSERT INTO employee (first_name, last_name, login_id, password_hash, role_id, store_id, is_active)
VALUES
('Store', 'Manager', '1001', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', 1, 1, TRUE), -- password: 1234
('Store', 'Cashier', '2001', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', 2, 1, TRUE); -- password: 1234

-- Add a comment to indicate the script has completed successfully
-- This schema matches the TruPOS Spring Boot backend implementation