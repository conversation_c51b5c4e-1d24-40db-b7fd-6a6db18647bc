#!/bin/bash

# Setup Sample Data Script
# This script creates sample data using the TruPOS API
# Replaces manual SQL data insertion

set -e  # Exit on any error

# Configuration
API_BASE_URL="${API_BASE_URL:-http://localhost:8080/api}"
VERBOSE="${VERBOSE:-false}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if API is available
check_api() {
    print_status "Checking API availability at $API_BASE_URL..."
    
    if curl -s -f "$API_BASE_URL/stores" > /dev/null 2>&1; then
        print_success "API is available"
        return 0
    else
        print_error "API is not available at $API_BASE_URL"
        print_error "Please ensure the backend is running and accessible"
        exit 1
    fi
}

# Function to create a store
create_store() {
    local store_data='
    {
        "storeName": "TruPOS Main Store",
        "address": "123 Main St",
        "city": "Anytown",
        "state": "CA",
        "zipCode": "90210",
        "phoneNumber": "************",
        "taxRate": 0.0725,
        "storeNo": 1,
        "wicStateId": 123456
    }'
    
    print_status "Creating sample store..."
    
    local response=$(curl -s -w "%{http_code}" -X POST "$API_BASE_URL/stores" \
        -H "Content-Type: application/json" \
        -d "$store_data")
    
    local http_code="${response: -3}"
    local body="${response%???}"
    
    if [ "$http_code" = "201" ] || [ "$http_code" = "200" ]; then
        print_success "Store created successfully"
        if [ "$VERBOSE" = "true" ]; then
            echo "Response: $body"
        fi
        return 0
    else
        print_error "Failed to create store - HTTP $http_code"
        if [ "$VERBOSE" = "true" ]; then
            echo "Response: $body"
        fi
        return 1
    fi
}

# Function to create departments
create_departments() {
    local departments=(
        '{"departmentName": "Grocery", "departmentDescription": "General grocery items", "isActive": true}'
        '{"departmentName": "Produce", "departmentDescription": "Fresh fruits and vegetables", "isActive": true}'
        '{"departmentName": "Dairy", "departmentDescription": "Milk, cheese, and dairy products", "isActive": true}'
        '{"departmentName": "Meat", "departmentDescription": "Fresh meat and poultry", "isActive": true}'
    )
    
    print_status "Creating sample departments..."
    
    local success_count=0
    local total_count=${#departments[@]}
    
    for dept_data in "${departments[@]}"; do
        local response=$(curl -s -w "%{http_code}" -X POST "$API_BASE_URL/departments" \
            -H "Content-Type: application/json" \
            -d "$dept_data")
        
        local http_code="${response: -3}"
        local body="${response%???}"
        
        if [ "$http_code" = "201" ] || [ "$http_code" = "200" ]; then
            ((success_count++))
            local dept_name=$(echo "$dept_data" | grep -o '"departmentName":"[^"]*"' | cut -d'"' -f4)
            print_success "Department '$dept_name' created"
            if [ "$VERBOSE" = "true" ]; then
                echo "Response: $body"
            fi
        else
            local dept_name=$(echo "$dept_data" | grep -o '"departmentName":"[^"]*"' | cut -d'"' -f4)
            print_error "Failed to create department '$dept_name' - HTTP $http_code"
            if [ "$VERBOSE" = "true" ]; then
                echo "Response: $body"
            fi
        fi
    done
    
    print_status "Created $success_count/$total_count departments"
    return 0
}

# Function to create sample customers
create_customers() {
    local customers=(
        '{"loyaltyCardNumber": "1000001", "firstName": "John", "lastName": "Doe", "phoneNumber": "************", "email": "<EMAIL>", "isActive": true}'
        '{"loyaltyCardNumber": "1000002", "firstName": "Jane", "lastName": "Smith", "phoneNumber": "************", "email": "<EMAIL>", "isActive": true}'
    )
    
    print_status "Creating sample customers..."
    
    local success_count=0
    local total_count=${#customers[@]}
    
    for customer_data in "${customers[@]}"; do
        local response=$(curl -s -w "%{http_code}" -X POST "$API_BASE_URL/customers" \
            -H "Content-Type: application/json" \
            -d "$customer_data")
        
        local http_code="${response: -3}"
        local body="${response%???}"
        
        if [ "$http_code" = "201" ] || [ "$http_code" = "200" ]; then
            ((success_count++))
            local customer_name=$(echo "$customer_data" | grep -o '"firstName":"[^"]*"' | cut -d'"' -f4)
            print_success "Customer '$customer_name' created"
            if [ "$VERBOSE" = "true" ]; then
                echo "Response: $body"
            fi
        else
            local customer_name=$(echo "$customer_data" | grep -o '"firstName":"[^"]*"' | cut -d'"' -f4)
            print_error "Failed to create customer '$customer_name' - HTTP $http_code"
            if [ "$VERBOSE" = "true" ]; then
                echo "Response: $body"
            fi
        fi
    done
    
    print_status "Created $success_count/$total_count customers"
    return 0
}

# Function to create sample employees
create_employees() {
    local employees=(
        '{"firstName": "Store", "lastName": "Manager", "loginId": "1001", "email": "<EMAIL>", "isActive": true, "role": {"roleId": 1}, "store": {"storeId": 1}}'
        '{"firstName": "Store", "lastName": "Cashier", "loginId": "2001", "email": "<EMAIL>", "isActive": true, "role": {"roleId": 2}, "store": {"storeId": 1}}'
    )
    
    local passwords=("1234" "1234")
    
    print_status "Creating sample employees..."
    
    local success_count=0
    local total_count=${#employees[@]}
    
    for i in "${!employees[@]}"; do
        local employee_data="${employees[$i]}"
        local password="${passwords[$i]}"
        
        local response=$(curl -s -w "%{http_code}" -X POST "$API_BASE_URL/employees?password=$password" \
            -H "Content-Type: application/json" \
            -d "$employee_data")
        
        local http_code="${response: -3}"
        local body="${response%???}"
        
        if [ "$http_code" = "201" ] || [ "$http_code" = "200" ]; then
            ((success_count++))
            local employee_name=$(echo "$employee_data" | grep -o '"firstName":"[^"]*"' | cut -d'"' -f4)
            print_success "Employee '$employee_name' created"
            if [ "$VERBOSE" = "true" ]; then
                echo "Response: $body"
            fi
        else
            local employee_name=$(echo "$employee_data" | grep -o '"firstName":"[^"]*"' | cut -d'"' -f4)
            print_error "Failed to create employee '$employee_name' - HTTP $http_code"
            if [ "$VERBOSE" = "true" ]; then
                echo "Response: $body"
            fi
        fi
    done
    
    print_status "Created $success_count/$total_count employees"
    return 0
}

# Main execution
main() {
    echo "=================================================="
    echo "TruPOS Sample Data Setup Script"
    echo "=================================================="
    echo "API Base URL: $API_BASE_URL"
    echo "=================================================="
    
    # Check API availability
    check_api
    
    # Create sample data
    print_status "Setting up sample data..."
    
    # Create store
    create_store
    
    # Create departments
    create_departments
    
    # Create customers
    create_customers
    
    # Create employees (requires roles to exist first)
    print_warning "Note: Employee creation may fail if roles don't exist in the database"
    print_warning "Ensure roles are created via database migration or manual setup"
    create_employees
    
    echo "=================================================="
    print_success "Sample data setup completed!"
    echo "=================================================="
    
    print_status "You can now:"
    echo "  - View stores: GET $API_BASE_URL/stores"
    echo "  - View departments: GET $API_BASE_URL/departments"
    echo "  - View customers: GET $API_BASE_URL/customers"
    echo "  - View employees: GET $API_BASE_URL/employees"
    echo "  - Test login with employee ID 1001, password: 1234"
}

# Help function
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Create sample data for TruPOS using the API"
    echo ""
    echo "Options:"
    echo "  -h, --help              Show this help message"
    echo "  -u, --url URL           API base URL (default: http://localhost:8080/api)"
    echo "  -v, --verbose           Enable verbose output"
    echo ""
    echo "Environment Variables:"
    echo "  API_BASE_URL           API base URL"
    echo "  VERBOSE               Enable verbose output (true/false)"
    echo ""
    echo "Examples:"
    echo "  $0                                   # Use defaults"
    echo "  $0 -u http://localhost:8080/api      # Custom API URL"
    echo "  $0 -v                                # Verbose output"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -u|--url)
            API_BASE_URL="$2"
            shift 2
            ;;
        -v|--verbose)
            VERBOSE="true"
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Run main function
main
