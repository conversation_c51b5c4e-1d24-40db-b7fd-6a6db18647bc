#!/bin/bash

# Reset Employee Passwords Script
# This script resets all employee passwords to a default value using the API
# Replaces: set_default_employee_passwords.sql

set -e  # Exit on any error

# Configuration
API_BASE_URL="${API_BASE_URL:-http://localhost:8080/api}"
DEFAULT_PASSWORD="${DEFAULT_PASSWORD:-123456}"
VERBOSE="${VERBOSE:-false}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if API is available
check_api() {
    print_status "Checking API availability at $API_BASE_URL..."
    
    if curl -s -f "$API_BASE_URL/employees" > /dev/null 2>&1; then
        print_success "API is available"
        return 0
    else
        print_error "API is not available at $API_BASE_URL"
        print_error "Please ensure the backend is running and accessible"
        exit 1
    fi
}

# Function to get all employees
get_employees() {
    print_status "Fetching all employees..."
    
    local response=$(curl -s -X GET "$API_BASE_URL/employees" \
        -H "Content-Type: application/json")
    
    if [ $? -eq 0 ]; then
        echo "$response"
    else
        print_error "Failed to fetch employees"
        exit 1
    fi
}

# Function to reset password for a single employee
reset_employee_password() {
    local employee_id=$1
    local employee_name=$2
    
    print_status "Resetting password for employee ID $employee_id ($employee_name)..."
    
    local response=$(curl -s -w "%{http_code}" -X PUT "$API_BASE_URL/employees/$employee_id/password" \
        -H "Content-Type: application/json" \
        -d "{\"password\": \"$DEFAULT_PASSWORD\"}")
    
    local http_code="${response: -3}"
    local body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        print_success "Password reset for employee ID $employee_id ($employee_name)"
        if [ "$VERBOSE" = "true" ]; then
            echo "Response: $body"
        fi
        return 0
    else
        print_error "Failed to reset password for employee ID $employee_id ($employee_name) - HTTP $http_code"
        if [ "$VERBOSE" = "true" ]; then
            echo "Response: $body"
        fi
        return 1
    fi
}

# Main execution
main() {
    echo "=================================================="
    echo "Employee Password Reset Script"
    echo "=================================================="
    echo "API Base URL: $API_BASE_URL"
    echo "Default Password: $DEFAULT_PASSWORD"
    echo "=================================================="
    
    # Check API availability
    check_api
    
    # Get all employees
    local employees_json=$(get_employees)
    
    # Parse employee data and reset passwords
    local employee_count=0
    local success_count=0
    local failure_count=0
    
    # Extract employee IDs and names using basic text processing
    # Note: This is a simple approach. For production, consider using jq for JSON parsing
    echo "$employees_json" | grep -o '"employeeId":[0-9]*' | while read -r line; do
        local employee_id=$(echo "$line" | grep -o '[0-9]*')
        
        # Get employee name (simplified extraction)
        local employee_name=$(echo "$employees_json" | grep -A 5 -B 5 "\"employeeId\":$employee_id" | grep -o '"firstName":"[^"]*"' | head -1 | cut -d'"' -f4)
        local last_name=$(echo "$employees_json" | grep -A 5 -B 5 "\"employeeId\":$employee_id" | grep -o '"lastName":"[^"]*"' | head -1 | cut -d'"' -f4)
        
        if [ -n "$last_name" ]; then
            employee_name="$employee_name $last_name"
        fi
        
        if [ -n "$employee_id" ]; then
            ((employee_count++))
            
            if reset_employee_password "$employee_id" "$employee_name"; then
                ((success_count++))
            else
                ((failure_count++))
            fi
        fi
    done
    
    echo "=================================================="
    echo "Password Reset Summary:"
    echo "Total Employees: $employee_count"
    echo "Successful Resets: $success_count"
    echo "Failed Resets: $failure_count"
    echo "=================================================="
    
    if [ $failure_count -eq 0 ]; then
        print_success "All employee passwords have been reset successfully!"
        exit 0
    else
        print_warning "Some password resets failed. Check the output above for details."
        exit 1
    fi
}

# Help function
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Reset all employee passwords to a default value using the TruPOS API"
    echo ""
    echo "Options:"
    echo "  -h, --help              Show this help message"
    echo "  -u, --url URL           API base URL (default: http://localhost:8080/api)"
    echo "  -p, --password PASS     Default password (default: 123456)"
    echo "  -v, --verbose           Enable verbose output"
    echo ""
    echo "Environment Variables:"
    echo "  API_BASE_URL           API base URL"
    echo "  DEFAULT_PASSWORD       Default password to set"
    echo "  VERBOSE               Enable verbose output (true/false)"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Use defaults"
    echo "  $0 -u http://localhost:8080/api      # Custom API URL"
    echo "  $0 -p newpassword123                 # Custom default password"
    echo "  $0 -v                                # Verbose output"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -u|--url)
            API_BASE_URL="$2"
            shift 2
            ;;
        -p|--password)
            DEFAULT_PASSWORD="$2"
            shift 2
            ;;
        -v|--verbose)
            VERBOSE="true"
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Run main function
main
