#!/bin/bash

# Simple Transaction Test Script
# Tests the complete transaction flow with manual API calls

set -e  # Exit on any error

# Configuration
API_BASE_URL="${API_BASE_URL:-http://localhost:8080/api}"
STORE_ID="${STORE_ID:-1}"
LANE_ID="${LANE_ID:-1}"
EMPLOYEE_ID="${EMPLOYEE_ID:-1}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

print_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_data() {
    echo -e "${CYAN}[DATA]${NC} $1"
}

echo "=================================================================="
echo "🧪 Simple TruPOS Transaction Test"
echo "=================================================================="
echo "API Base URL: $API_BASE_URL"
echo "Store ID: $STORE_ID, Lane ID: $LANE_ID, Employee ID: $EMPLOYEE_ID"
echo "=================================================================="

# Step 1: Check API availability
print_step "1. Checking API availability"
if curl -s -f "$API_BASE_URL/products/active" > /dev/null 2>&1; then
    print_success "API is available"
else
    print_error "API is not available. Please start the backend."
    exit 1
fi

# Step 2: Create a new transaction
print_step "2. Creating new transaction"
TRANSACTION_RESPONSE=$(curl -s -X POST "$API_BASE_URL/transactions?storeId=$STORE_ID&laneId=$LANE_ID&employeeId=$EMPLOYEE_ID")
echo "$TRANSACTION_RESPONSE" | grep -q "transactionId"
if [ $? -eq 0 ]; then
    TRANSACTION_ID=$(echo "$TRANSACTION_RESPONSE" | grep -o '"transactionId":[0-9]*' | cut -d':' -f2)
    print_success "Transaction created with ID: $TRANSACTION_ID"
    print_data "Response: $TRANSACTION_RESPONSE"
else
    print_error "Failed to create transaction"
    print_error "Response: $TRANSACTION_RESPONSE"
    exit 1
fi

# Step 3: Add first item to transaction
print_step "3. Adding first item (Item ID: 5, Quantity: 2, Price: 3.99)"
ITEM_RESPONSE=$(curl -s -X POST "$API_BASE_URL/transactions/$TRANSACTION_ID/line-items?itemId=5&quantity=2&unitPrice=3.99")
echo "$ITEM_RESPONSE" | grep -q "line_number"
if [ $? -eq 0 ]; then
    print_success "First item added successfully"
    print_data "Response: $ITEM_RESPONSE"
else
    print_error "Failed to add first item"
    print_error "Response: $ITEM_RESPONSE"
    exit 1
fi

# Step 4: Add second item to transaction
print_step "4. Adding second item (Item ID: 7, Quantity: 1, Price: 2.49)"
ITEM2_RESPONSE=$(curl -s -X POST "$API_BASE_URL/transactions/$TRANSACTION_ID/line-items?itemId=7&quantity=1&unitPrice=2.49")
echo "$ITEM2_RESPONSE" | grep -q "line_number"
if [ $? -eq 0 ]; then
    print_success "Second item added successfully"
    print_data "Response: $ITEM2_RESPONSE"
else
    print_error "Failed to add second item"
    print_error "Response: $ITEM2_RESPONSE"
    exit 1
fi

# Step 5: Get transaction details
print_step "5. Getting updated transaction details"
TRANSACTION_DETAILS=$(curl -s -X GET "$API_BASE_URL/transactions/$TRANSACTION_ID")
echo "$TRANSACTION_DETAILS" | grep -q "totalAmount"
if [ $? -eq 0 ]; then
    TOTAL_AMOUNT=$(echo "$TRANSACTION_DETAILS" | grep -o '"totalAmount":[0-9.]*' | cut -d':' -f2)
    ITEM_COUNT=$(echo "$TRANSACTION_DETAILS" | grep -o '"itemCount":[0-9]*' | cut -d':' -f2)
    print_success "Transaction details retrieved"
    print_info "Item Count: $ITEM_COUNT"
    print_info "Total Amount: \$$TOTAL_AMOUNT"
    print_data "Full Response: $TRANSACTION_DETAILS"
else
    print_error "Failed to get transaction details"
    print_error "Response: $TRANSACTION_DETAILS"
    exit 1
fi

# Step 6: Add tender (payment)
print_step "6. Adding cash tender for \$$TOTAL_AMOUNT"
TENDER_RESPONSE=$(curl -s -X POST "$API_BASE_URL/transactions/$TRANSACTION_ID/tenders?tenderType=CASH&amount=$TOTAL_AMOUNT")
echo "$TENDER_RESPONSE" | grep -q "tender_type"
if [ $? -eq 0 ]; then
    print_success "Cash tender added successfully"
    print_data "Response: $TENDER_RESPONSE"
else
    print_error "Failed to add tender"
    print_error "Response: $TENDER_RESPONSE"
    exit 1
fi

# Step 7: Complete the transaction
print_step "7. Completing transaction"
COMPLETE_RESPONSE=$(curl -s -X PUT "$API_BASE_URL/transactions/$TRANSACTION_ID/complete")
echo "$COMPLETE_RESPONSE" | grep -q "COMPLETED"
if [ $? -eq 0 ]; then
    print_success "Transaction completed successfully!"
    print_data "Response: $COMPLETE_RESPONSE"
else
    print_error "Failed to complete transaction"
    print_error "Response: $COMPLETE_RESPONSE"
    exit 1
fi

# Step 8: Verify completed transaction
print_step "8. Verifying completed transaction"
FINAL_DETAILS=$(curl -s -X GET "$API_BASE_URL/transactions/$TRANSACTION_ID")
STATUS=$(echo "$FINAL_DETAILS" | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
if [ "$STATUS" = "COMPLETED" ]; then
    print_success "Transaction verification successful!"
    print_info "Final Status: $STATUS"
else
    print_error "Transaction verification failed. Status: $STATUS"
    exit 1
fi

echo ""
echo "=================================================================="
echo "✅ Transaction Test Results"
echo "=================================================================="
print_success "Complete transaction flow tested successfully!"
print_info "Transaction ID: $TRANSACTION_ID"
print_info "Status: $STATUS"
print_info "Items: $ITEM_COUNT"
print_info "Total: \$$TOTAL_AMOUNT"
echo ""
print_info "You can view the transaction in Swagger UI:"
print_data "http://localhost:8080/api/swagger-ui/index.html"
print_data "GET /transactions/$TRANSACTION_ID"
echo "=================================================================="
