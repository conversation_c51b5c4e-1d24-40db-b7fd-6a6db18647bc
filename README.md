# TruPOS Backend - Spring Boot API

Modern Spring Boot REST API for point of sale operations.

## 🏗️ Project Structure

```
trupos-backend-spring/
├── backend/                # Spring Boot application
│   ├── src/main/java/     # Java source code
│   │   └── com/trupos/    # Application packages
│   │       ├── controller/ # REST controllers
│   │       ├── service/   # Business logic
│   │       ├── repository/ # Data access
│   │       ├── entity/    # JPA entities
│   │       ├── dto/       # Data transfer objects
│   │       └── config/    # Configuration
│   ├── src/main/resources/ # Configuration files
│   ├── target/            # Build artifacts
│   └── pom.xml           # Maven configuration
├── database/              # Database setup and scripts
├── docs/                  # API documentation
└── scripts/              # Development and deployment scripts
```

## 🚀 Quick Start

### Prerequisites
- Java 17+
- Maven 3.6+
- PostgreSQL (for production) or H2 (for development)

### Start Development Server
```bash
# Option 1: Use the startup script (recommended)
./scripts/development/start-backend.sh

# Option 2: Manual startup
cd backend
mvn spring-boot:run

# API will be available at http://localhost:8080/api
# API Documentation at http://localhost:8080/swagger-ui.html
```

### Build for Production
```bash
# Build JAR file
mvn clean package

# Run production build
java -jar target/trupos-api-0.0.1-SNAPSHOT.jar
```

## 🎯 Backend Development

### ⚙️ Technology Stack
- **Spring Boot 3.2.1** - Application framework
- **Java 17** - Programming language
- **Spring Data JPA** - Data access layer
- **H2 Database** - Development database
- **PostgreSQL** - Production database
- **Maven** - Build tool
- **Swagger/OpenAPI** - API documentation

### 🔌 API Endpoints
The TruPOS backend provides REST API endpoints:
- **Base URL**: http://localhost:8080/api
- **API Documentation**: http://localhost:8080/swagger-ui.html
- **Health Check**: http://localhost:8080/actuator/health

## ✨ Features

- **Product Management**: Comprehensive catalog with barcode scanning support
- **Customer Management**: Customer profiles and loyalty tracking
- **Transaction Processing**: Complete POS workflow with multiple payment types
- **Employee Management**: Authentication and role-based access control
- **Store Management**: Multi-store support with lane configuration
- **Reporting**: Sales analytics and business intelligence APIs
- **Security**: Session-based authentication with manager authorization
- **Database Support**: H2 for development, PostgreSQL for production

## 🔧 Development Commands

### Backend Development
```bash
# Start development server
./scripts/development/start-backend.sh

# Or manual commands
cd backend
mvn spring-boot:run      # Start development server
mvn clean package       # Build JAR file
mvn test                 # Run tests
mvn clean install       # Clean build and install
mvn spring-boot:build-image  # Build Docker image
```

### Database Management
```bash
# Start PostgreSQL with Docker
cd backend
docker-compose up postgres

# Access database admin (pgAdmin)
# http://localhost:5050 (<EMAIL> / admin)
```

### Testing & Quality
```bash
mvn test                 # Run all tests
mvn test -Dtest=CustomerControllerTest  # Run specific test
mvn test jacoco:report   # Run tests with coverage
```

## 📊 Development Services

| Service | URL | Port | Description |
|---------|-----|------|-------------|
| Backend API | http://localhost:8080/api | 8080 | REST API endpoints |
| API Documentation | http://localhost:8080/swagger-ui.html | 8080 | Interactive API docs |
| Health Check | http://localhost:8080/actuator/health | 8080 | Application health status |
| PostgreSQL | localhost:5432 | 5432 | Database server |
| pgAdmin | http://localhost:5050 | 5050 | Database administration |

## 🏢 Backend Architecture

### Layer Structure
- **Controllers**: REST API endpoints and request handling
- **Services**: Business logic and transaction management
- **Repositories**: Data access layer with Spring Data JPA
- **Entities**: JPA entities representing database tables
- **DTOs**: Data transfer objects for API communication
- **Configuration**: Security, CORS, and application configuration

### Production Ready
- **Spring Security**: Authentication and authorization
- **Database Migration**: Flyway support for schema changes
- **Monitoring**: Actuator endpoints for health and metrics
- **Logging**: Configurable logging with rotation
- **Docker Support**: Container-ready with Docker Compose

## 📚 Documentation

### API Documentation
- [Interactive API Docs](http://localhost:8080/swagger-ui.html)
- [Database Schema](./database/README.md)
- [Architecture Guide](./docs/api/architecture-guide.md)
- [Security Guide](./backend/docs/security.md)

### Development Guides
- [Getting Started](./docs/development/quick-start.md)
- [Database Setup](./database/DATABASE-SETUP.md)
- [Deployment Guide](./docs/deployment/)
- [API Integration](./docs/api/)

## 🤝 Contributing

### Development Workflow
1. Work in the `backend/src/` directory for API changes
2. Follow Java and Spring Boot best practices
3. Update API documentation as needed
4. Ensure database compatibility

### Pull Request Process
1. Create feature branch
2. Make changes and add tests
3. Run tests and ensure they pass
4. Update documentation
5. Submit pull request for review

## 🔒 Security

- Session-based authentication
- Role-based access control (RBAC)
- Manager authorization for sensitive operations
- Input validation and sanitization
- CORS configuration for frontend integration
- Secure password handling with BCrypt

## 📈 Performance

- Optimized JPA queries with proper indexing
- Connection pooling for database access
- Caching strategies for frequently accessed data
- Efficient pagination for large datasets
- Monitoring with Spring Boot Actuator

## 🚀 Deployment

### Development
```bash
cd backend
mvn spring-boot:run
```

### Production
```bash
cd backend
mvn clean package
java -jar target/trupos-api-0.0.1-SNAPSHOT.jar
```

### Docker Deployment
```bash
cd backend
mvn spring-boot:build-image
docker run -p 8080:8080 trupos-api:0.0.1-SNAPSHOT
```

### Environment Configuration
```bash
# Environment variables for production
DB_URL=***************************************
DB_USERNAME=trupos_user
DB_PASSWORD=secure_password
CORS_ORIGINS=https://your-frontend-domain.com
```

## 📞 Support

### Development Areas
- **REST API**: Spring Boot controllers and endpoints
- **Business Logic**: Service layer implementation
- **Data Access**: JPA repositories and database integration
- **Security**: Authentication and authorization

### Resources
- [Spring Boot Documentation](https://spring.io/projects/spring-boot)
- [Spring Data JPA](https://spring.io/projects/spring-data-jpa)
- [API Documentation](http://localhost:8080/swagger-ui.html)

## 📄 License

MIT License - see LICENSE file for details

---

**TruPOS Backend** - Spring Boot REST API for point of sale operations
