package com.trupos.repositories;

import com.trupos.entities.Role;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface RoleRepository extends JpaRepository<Role, Long> {

    /**
     * Find role by name
     */
    Optional<Role> findByRoleName(String roleName);

    /**
     * Find roles containing specific text in name
     */
    @Query("SELECT r FROM Role r WHERE LOWER(r.roleName) LIKE LOWER(CONCAT('%', :searchTerm, '%'))")
    List<Role> findByRoleNameContaining(@Param("searchTerm") String searchTerm);

    /**
     * Check if role name exists
     */
    boolean existsByRoleName(String roleName);

    /**
     * Find manager roles
     */
    @Query("SELECT r FROM Role r WHERE LOWER(r.roleName) LIKE '%manager%'")
    List<Role> findManagerRoles();

    /**
     * Find roles with specific permission
     */
    @Query("SELECT r FROM Role r JOIN r.permissions p WHERE p.permissionName = :permissionName")
    List<Role> findRolesWithPermission(@Param("permissionName") String permissionName);
}
