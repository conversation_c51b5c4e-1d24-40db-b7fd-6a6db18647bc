package com.trupos.repositories;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.trupos.entities.Employee;
import com.trupos.entities.Store;

@Repository
public interface EmployeeRepository extends JpaRepository<Employee, Long> {

    /**
     * Find employee by login ID
     */
    Optional<Employee> findByLoginId(String loginId);

    /**
     * Find employees by store ID
     */
    List<Employee> findByStoreId(Long storeId);

    /**
     * Find employees by email
     */
    Optional<Employee> findByEmail(String email);

    /**
     * Find active employees
     */
    List<Employee> findByIsActiveTrue();

    /**
     * Find active employees by store
     */
    List<Employee> findByStoreAndIsActiveTrue(Store store);

    /**
     * Find managers (employees with manager role)
     */
    @Query("SELECT e FROM Employee e WHERE LOWER(e.role.roleName) LIKE '%manager%'")
    List<Employee> findManagers();

    /**
     * Search employees by name
     */
    @Query("SELECT e FROM Employee e WHERE " +
           "LOWER(e.firstName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(e.lastName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(CONCAT(e.firstName, ' ', e.lastName)) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(e.loginId) LIKE LOWER(CONCAT('%', :searchTerm, '%'))")
    List<Employee> searchByName(@Param("searchTerm") String searchTerm);
}
