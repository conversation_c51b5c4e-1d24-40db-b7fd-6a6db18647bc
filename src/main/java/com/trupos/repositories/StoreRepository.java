package com.trupos.repositories;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.trupos.entities.Store;

@Repository
public interface StoreRepository extends JpaRepository<Store, Long> {

    /**
     * Find store by store number
     */
    Optional<Store> findByStoreNo(Integer storeNo);

    /**
     * Find store by name
     */
    Optional<Store> findByStoreName(String storeName);

    /**
     * Search stores by name or location
     */
    @Query("SELECT s FROM Store s WHERE " +
           "LOWER(s.storeName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(s.city) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(s.state) LIKE LOWER(CONCAT('%', :searchTerm, '%'))")
    List<Store> searchStores(@Param("searchTerm") String searchTerm);

    /**
     * Find stores by state
     */
    List<Store> findByState(String state);

    /**
     * Find stores by city
     */
    List<Store> findByCity(String city);

    /**
     * Check if store number exists
     */
    boolean existsByStoreNo(Integer storeNo);

    /**
     * Check if store name exists
     */
    boolean existsByStoreName(String storeName);
}
