package com.trupos.repositories;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.trupos.entities.Customer;
import com.trupos.entities.Employee;
import com.trupos.entities.Lane;
import com.trupos.entities.Store;
import com.trupos.entities.Transaction;

@Repository
public interface TransactionRepository extends JpaRepository<Transaction, Long> {

    /**
     * Find transactions by store
     */
    List<Transaction> findByStore(Store store);

    /**
     * Find transactions by lane
     */
    List<Transaction> findByLane(Lane lane);

    /**
     * Find transactions by employee
     */
    List<Transaction> findByEmployee(Employee employee);

    /**
     * Find transactions by customer
     */
    List<Transaction> findByCustomer(Customer customer);

    /**
     * Find transactions by status (now using String)
     */
    List<Transaction> findByStatus(String status);

    /**
     * Find transactions by date range
     */
    @Query("SELECT t FROM Transaction t WHERE t.transactionTimestamp BETWEEN :startDate AND :endDate")
    List<Transaction> findByDateRange(@Param("startDate") LocalDateTime startDate,
                                      @Param("endDate") LocalDateTime endDate);

    /**
     * Find transactions by date range and store
     */
    @Query("SELECT t FROM Transaction t WHERE t.store = :store AND t.transactionTimestamp BETWEEN :startDate AND :endDate")
    List<Transaction> findByStoreAndDateRange(@Param("store") Store store,
                                              @Param("startDate") LocalDateTime startDate,
                                              @Param("endDate") LocalDateTime endDate);

    /**
     * Find by status, ordered descending by time (use String)
     */
    List<Transaction> findByStatusOrderByTransactionTimestampDesc(String status);

    /**
     * Find suspended transactions by lane
     */
    @Query("SELECT t FROM Transaction t WHERE t.lane = :lane AND t.status = 'SUSPENDED'")
    List<Transaction> findSuspendedTransactionsByLane(@Param("lane") Lane lane);

    /**
     * Find voided transactions
     */
    List<Transaction> findByIsVoidedTrue();

    /**
     * Find return transactions
     */
    @Query("SELECT t FROM Transaction t WHERE t.originalTransactionId IS NOT NULL")
    List<Transaction> findReturnTransactions();

    /**
     * Find transactions with pagination
     */
    Page<Transaction> findByStoreOrderByTransactionTimestampDesc(Store store, Pageable pageable);

    /**
     * Find transactions by store and status with pagination (use String status)
     */
    Page<Transaction> findByStoreAndStatusOrderByTransactionTimestampDesc(Store store, String status, Pageable pageable);

    /**
     * Calculate total sales for date range
     */
    @Query("SELECT COALESCE(SUM(t.totalAmount), 0) FROM Transaction t WHERE " +
            "t.store = :store AND t.status = 'COMPLETED' AND t.isVoided = false AND " +
            "t.transactionTimestamp BETWEEN :startDate AND :endDate")
    BigDecimal calculateTotalSales(@Param("store") Store store,
                                   @Param("startDate") LocalDateTime startDate,
                                   @Param("endDate") LocalDateTime endDate);

    /**
     * Calculate total tax for date range
     */
    @Query("SELECT COALESCE(SUM(t.taxAmount), 0) FROM Transaction t WHERE " +
            "t.store = :store AND t.status = 'COMPLETED' AND t.isVoided = false AND " +
            "t.transactionTimestamp BETWEEN :startDate AND :endDate")
    BigDecimal calculateTotalTax(@Param("store") Store store,
                                 @Param("startDate") LocalDateTime startDate,
                                 @Param("endDate") LocalDateTime endDate);

    /**
     * Count transactions by status and date range (status as String)
     */
    @Query("SELECT COUNT(t) FROM Transaction t WHERE " +
            "t.store = :store AND t.status = :status AND " +
            "t.transactionTimestamp BETWEEN :startDate AND :endDate")
    long countByStatusAndDateRange(@Param("store") Store store,
                                   @Param("status") String status,
                                   @Param("startDate") LocalDateTime startDate,
                                   @Param("endDate") LocalDateTime endDate);

    /**
     * Find transactions by employee and date range
     */
    @Query("SELECT t FROM Transaction t WHERE t.employee = :employee AND " +
            "t.transactionTimestamp BETWEEN :startDate AND :endDate " +
            "ORDER BY t.transactionTimestamp DESC")
    List<Transaction> findByEmployeeAndDateRange(@Param("employee") Employee employee,
                                                 @Param("startDate") LocalDateTime startDate,
                                                 @Param("endDate") LocalDateTime endDate);

}