package com.trupos.repositories;

import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.trupos.entities.Customer;

@Repository
public interface CustomerRepository extends JpaRepository<Customer, Long> {

    /**
     * Find customer by loyalty card number.
     *
     * @param loyaltyCardNumber the loyalty card number
     * @return an Optional containing the customer if found, or empty otherwise
     */
    Optional<Customer> findByLoyaltyCardNumber(String loyaltyCardNumber);

    /**
     * Find customer by phone number (exact match).
     *
     * @param phoneNumber the customer's phone number
     * @return an Optional containing the customer if found, or empty otherwise
     */
    Optional<Customer> findByPhoneNumber(String phoneNumber);

    /**
     * Find customer by email.
     *
     * @param email the customer's email
     * @return an Optional containing the customer if found, or empty otherwise
     */
    Optional<Customer> findByEmail(String email);

    /**
     * Find all active customers.
     *
     * @return a list of all active customers
     */
    List<Customer> findByIsActiveTrue();

    /**
     * Find customers by partial (containing) phone number.
     *
     * @param phoneNumber a fragment or whole phone number to search for
     * @return a list of matching customers
     */
    @Query("SELECT c FROM Customer c WHERE c.phoneNumber LIKE CONCAT('%', :phoneNumber, '%')")
    List<Customer> findByPhoneNumberContaining(@Param("phoneNumber") String phoneNumber);

    /**
     * Search customers across multiple fields (name, email, phone, loyalty card) with pagination.
     *
     * @param searchTerm the search term to filter customers (nullable)
     * @param pageable the paging information
     * @return a page of matching customers
     */
    @Query("SELECT c FROM Customer c WHERE " +
            "(:searchTerm IS NULL OR " +
            "LOWER(c.firstName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
            "LOWER(c.lastName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
            "LOWER(c.email) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
            "c.phoneNumber LIKE CONCAT('%', :searchTerm, '%') OR " +
            "c.loyaltyCardNumber LIKE CONCAT('%', :searchTerm, '%'))")
    Page<Customer> search(@Param("searchTerm") String searchTerm, Pageable pageable);
}