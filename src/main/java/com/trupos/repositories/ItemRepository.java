package com.trupos.repositories;

import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.trupos.entities.Department;
import com.trupos.entities.Item;

@Repository
public interface ItemRepository extends JpaRepository<Item, Long> {

    /**
     * Find item by UPC
     */
    Optional<Item> findByUpc(String upc);

    /**
     * Find item by SKU
     */
    Optional<Item> findBySku(String sku);

    /**
     * Find items by department
     */
    List<Item> findByDepartment(Department department);

    /**
     * Find items by department ID
     */
    List<Item> findByDepartmentDepartmentId(Long departmentId);

    /**
     * Find active items
     */
    List<Item> findByIsActiveTrue();

    /**
     * Find active sellable items (excludes department sale items)
     */
    List<Item> findByIsActiveTrueAndIsDepartmentSaleFalse();

    /**
     * Find department sale items
     */
    List<Item> findByIsDepartmentSaleTrue();

    /**
     * Find active items by department
     */
    List<Item> findByDepartmentAndIsActiveTrue(Department department);

    /**
     * Find taxable items
     */
    List<Item> findByIsTaxableTrue();

    /**
     * Find scale items
     */
    List<Item> findByIsScaleItemTrue();

    /**
     * Find WIC eligible items
     */
    List<Item> findByIsWicEligibleTrue();

    /**
     * Find food stampable items
     */
    List<Item> findByIsFoodStampableTrue();

    /**
     * Find age restricted items
     */
    List<Item> findByIsAgeRestrictedTrue();

    /**
     * Search items by description
     */
    @Query("SELECT i FROM Item i WHERE " +
           "LOWER(i.description) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(i.brand) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "i.upc LIKE CONCAT('%', :searchTerm, '%') OR " +
           "i.sku LIKE CONCAT('%', :searchTerm, '%')")
    List<Item> searchItems(@Param("searchTerm") String searchTerm);

    /**
     * Search items with pagination
     */
    @Query("SELECT i FROM Item i WHERE " +
           "(:searchTerm IS NULL OR " +
           "LOWER(i.description) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(i.brand) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "i.upc LIKE CONCAT('%', :searchTerm, '%') OR " +
           "i.sku LIKE CONCAT('%', :searchTerm, '%')) AND " +
           "(:isActive IS NULL OR i.isActive = :isActive)")
    Page<Item> searchItemsWithPagination(@Param("searchTerm") String searchTerm, 
                                        @Param("isActive") Boolean isActive, 
                                        Pageable pageable);

    /**
     * Find items by UPC pattern (for barcode scanning)
     */
    @Query("SELECT i FROM Item i WHERE i.upc LIKE CONCAT(:upcPattern, '%')")
    List<Item> findByUpcStartingWith(@Param("upcPattern") String upcPattern);

    /**
     * Count active items
     */
    long countByIsActiveTrue();

    /**
     * Count items by department
     */
    long countByDepartment(Department department);
}
