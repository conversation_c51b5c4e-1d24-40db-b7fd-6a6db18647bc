package com.trupos.repositories;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.trupos.entities.Item;
import com.trupos.entities.ItemPrice;
import com.trupos.entities.Store;

@Repository
public interface ItemPriceRepository extends JpaRepository<ItemPrice, Long> {

    /**
     * Find current price for an item in a store
     */
    @Query("SELECT ip FROM ItemPrice ip WHERE ip.item = :item AND ip.store = :store AND " +
           "(ip.startDate IS NULL OR ip.startDate <= :now) AND " +
           "(ip.endDate IS NULL OR ip.endDate > :now)")
    Optional<ItemPrice> findCurrentPriceByItemAndStore(@Param("item") Item item,
                                                       @Param("store") Store store,
                                                       @Param("now") LocalDateTime now);

    /**
     * Find current price for an item in a store (using current time)
     */
    default Optional<ItemPrice> findCurrentPriceByItemAndStore(Item item, Store store) {
        return findCurrentPriceByItemAndStore(item, store, LocalDateTime.now());
    }

    /**
     * Find all prices for an item
     */
    List<ItemPrice> findByItem(Item item);

    /**
     * Find all prices for a store
     */
    List<ItemPrice> findByStore(Store store);

    /**
     * Find price history for an item in a store
     */
    @Query("SELECT ip FROM ItemPrice ip WHERE ip.item = :item AND ip.store = :store " +
           "ORDER BY ip.startDate DESC")
    List<ItemPrice> findPriceHistoryByItemAndStore(@Param("item") Item item, @Param("store") Store store);

    /**
     * Find active prices (current prices)
     */
    @Query("SELECT ip FROM ItemPrice ip WHERE " +
           "(ip.startDate IS NULL OR ip.startDate <= :now) AND " +
           "(ip.endDate IS NULL OR ip.endDate > :now)")
    List<ItemPrice> findActivePrices(@Param("now") LocalDateTime now);

    /**
     * Find expired prices
     */
    @Query("SELECT ip FROM ItemPrice ip WHERE ip.endDate IS NOT NULL AND ip.endDate <= :now")
    List<ItemPrice> findExpiredPrices(@Param("now") LocalDateTime now);
}
