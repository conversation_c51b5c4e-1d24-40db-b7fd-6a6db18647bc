package com.trupos.repositories;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.trupos.entities.Department;

@Repository
public interface DepartmentRepository extends JpaRepository<Department, Long> {

    /**
     * Find department by name
     */
    Optional<Department> findByDepartmentName(String departmentName);

    /**
     * Find active departments
     */
    List<Department> findByIsActiveTrue();

    /**
     * Search departments by name
     */
    @Query("SELECT d FROM Department d WHERE " +
           "LOWER(d.departmentName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(d.departmentDescription) LIKE LOWER(CONCAT('%', :searchTerm, '%'))")
    List<Department> searchDepartments(@Param("searchTerm") String searchTerm);

    /**
     * Check if department name exists
     */
    boolean existsByDepartmentName(String departmentName);

    /**
     * Count active departments
     */
    long countByIsActiveTrue();
}
