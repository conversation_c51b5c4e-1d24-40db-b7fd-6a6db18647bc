package com.trupos.repositories;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.trupos.entities.Lane;
import com.trupos.entities.Store;

@Repository
public interface LaneRepository extends JpaRepository<Lane, Long> {

    /**
     * Find lanes by store
     */
    List<Lane> findByStore(Store store);

    /**
     * Find lanes by store ID
     */
    List<Lane> findByStoreStoreId(Long storeId);

    /**
     * Find active lanes
     */
    List<Lane> findByIsActiveTrue();

    /**
     * Find active lanes by store
     */
    List<Lane> findByStoreAndIsActiveTrue(Store store);

    /**
     * Find lane by lane number and store
     */
    Optional<Lane> findByLaneNumberAndStore(Integer laneNumber, Store store);

    /**
     * Find express lanes
     */
    List<Lane> findByIsExpressTrue();

    /**
     * Find express lanes by store
     */
    List<Lane> findByStoreAndIsExpressTrue(Store store);

    /**
     * Check if lane number exists in store
     */
    @Query("SELECT COUNT(l) > 0 FROM Lane l WHERE l.laneNumber = :laneNumber AND l.store = :store")
    boolean existsByLaneNumberAndStore(@Param("laneNumber") Integer laneNumber, @Param("store") Store store);

    /**
     * Count active lanes by store
     */
    long countByStoreAndIsActiveTrue(Store store);
}
