package com.trupos.transformers;

import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.trupos.dtos.ItemPriceDTO;
import com.trupos.entities.Item;
import com.trupos.entities.ItemPrice;
import com.trupos.entities.Store;

/**
 * Function implementation to transform an ItemPriceDTO into an ItemPrice entity
 */
@Component
public class ItemPriceDTOToEntity implements Function<ItemPriceDTO, ItemPrice> {
    @Override
    public ItemPrice apply(ItemPriceDTO dto) {
        if (dto == null) return null;
        ItemPrice entity = new ItemPrice();
        entity.setId(dto.getId());
        if (dto.getItem() != null && dto.getItem().getId() != null) {
            Item i = new Item();
            i.setId(dto.getItem().getId());
            entity.setItem(i);
        }
        if (dto.getStore() != null && dto.getStore().getId() != null) {
            Store s = new Store();
            s.setId(dto.getStore().getId());
            entity.setStore(s);
        }
        entity.setPrice(dto.getPrice());
        entity.setStartDate(dto.getStartDate());
        entity.setEndDate(dto.getEndDate());
        entity.setCreatedAt(dto.getCreatedAt());
        entity.setUpdatedAt(dto.getUpdatedAt());
        return entity;
    }
}