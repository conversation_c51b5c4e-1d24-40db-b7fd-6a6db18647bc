package com.trupos.transformers;

import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.trupos.dtos.StoreDTO;
import com.trupos.entities.Store;

/**
 * Function implementation to transform a Store entity into a StoreDTO
 */
@Component
public class StoreToDTO implements Function<Store, StoreDTO> {
    @Override
    public StoreDTO apply(Store entity) {
        if (entity == null) return null;
        StoreDTO dto = new StoreDTO();
        dto.setId(entity.getId());
        dto.setName(entity.getName());
        dto.setAddress(entity.getAddress());
        dto.setCity(entity.getCity());
        dto.setState(entity.getState());
        dto.setZipCode(entity.getZipCode());
        dto.setPhoneNumber(entity.getPhoneNumber());
        dto.setTaxRate(entity.getTaxRate());
        dto.setStoreNo(entity.getStoreNo());
        dto.setWicStateId(entity.getWicStateId());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());
        return dto;
    }
}