package com.trupos.transformers;

import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.trupos.dtos.EmployeeDTO;
import com.trupos.entities.Employee;
import com.trupos.entities.Role;

/**
 * Function implementation to transform an Employee entity into an EmployeeDTO
 */
@Component
public class EmployeeToDTO implements Function<Employee, EmployeeDTO> {

    @Override
    public EmployeeDTO apply(Employee entity) {
        if (entity == null) return null;

        Role role = entity.getRole();

        return EmployeeDTO.builder()
                .id(entity.getId())
                .firstName(entity.getFirstName())
                .lastName(entity.getLastName())
                .loginId(entity.getLoginId())
                .email(entity.getEmail())
                .isActive(entity.getIsActive())
                .storeId(entity.getStoreId())
                .roleName(role != null ? role.getName() : null)
                .build();
    }
}
