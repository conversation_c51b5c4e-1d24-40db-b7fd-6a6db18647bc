package com.trupos.transformers;

import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.trupos.dtos.DepartmentDTO;
import com.trupos.entities.Department;

/**
 * Function implementation to transform a DepartmentDTO into a Department entity
 */
@Component
public class DepartmentDTOToEntity implements Function<DepartmentDTO, Department> {
    @Override
    public Department apply(DepartmentDTO dto) {
        if (dto == null) return null;
        Department entity = new Department();
        entity.setId(dto.getId());
        entity.setName(dto.getName());
        entity.setDescription(dto.getDescription());
        entity.setIsActive(dto.getIsActive());
        entity.setCreatedAt(dto.getCreatedAt());
        entity.setUpdatedAt(dto.getUpdatedAt());
        return entity;
    }
}