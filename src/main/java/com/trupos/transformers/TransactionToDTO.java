package com.trupos.transformers;

import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.trupos.dtos.CustomerDTO;
import com.trupos.dtos.EmployeeDTO;
import com.trupos.dtos.LaneDTO;
import com.trupos.dtos.StoreDTO;
import com.trupos.dtos.TransactionDTO;
import com.trupos.entities.Transaction;

/**
 * Function implementation to transform a Transaction entity into a TransactionDTO
 */
@Component
public class TransactionToDTO implements Function<Transaction, TransactionDTO> {
    @Override
    public TransactionDTO apply(Transaction entity) {
        if (entity == null) return null;
        TransactionDTO dto = new TransactionDTO();
        dto.setId(entity.getId());
        if (entity.getStore() != null) {
            StoreDTO s = new StoreDTO();
            s.setId(entity.getStore().getId());
            dto.setStore(s);
        }
        if (entity.getLane() != null) {
            LaneDTO l = new LaneDTO();
            l.setId(entity.getLane().getId());
            dto.setLane(l);
        }
        if (entity.getEmployee() != null) {
            EmployeeDTO e = new EmployeeDTO();
            e.setId(entity.getEmployee().getId());
            dto.setEmployee(e);
        }
        if (entity.getCustomer() != null) {
            CustomerDTO c = new CustomerDTO();
            c.setId(entity.getCustomer().getId());
            dto.setCustomer(c);
        }
        dto.setTimestamp(entity.getTimestamp());
        dto.setStatus(entity.getStatus());
        dto.setSubtotal(entity.getSubtotal());
        dto.setTaxAmount(entity.getTaxAmount());
        dto.setDiscountAmount(entity.getDiscountAmount());
        dto.setTotalAmount(entity.getTotalAmount());
        dto.setItemCount(entity.getItemCount());
        dto.setIsTraining(entity.getIsTraining());
        dto.setIsVoided(entity.getIsVoided());
        dto.setVoidReason(entity.getVoidReason());
        dto.setOriginalTransactionId(entity.getOriginalTransactionId());
        dto.setTransactionData(entity.getTransactionData());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());
        return dto;
    }
}