package com.trupos.transformers;

import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.trupos.dtos.StoreDTO;
import com.trupos.entities.Store;

/**
 * Function implementation to transform a StoreDTO into a Store entity
 */
@Component
public class StoreDTOToEntity implements Function<StoreDTO, Store> {
    @Override
    public Store apply(StoreDTO dto) {
        if (dto == null) return null;
        Store entity = new Store();
        entity.setId(dto.getId());
        entity.setName(dto.getName());
        entity.setAddress(dto.getAddress());
        entity.setCity(dto.getCity());
        entity.setState(dto.getState());
        entity.setZipCode(dto.getZipCode());
        entity.setPhoneNumber(dto.getPhoneNumber());
        entity.setTaxRate(dto.getTaxRate());
        entity.setStoreNo(dto.getStoreNo());
        entity.setWicStateId(dto.getWicStateId());
        entity.setCreatedAt(dto.getCreatedAt());
        entity.setUpdatedAt(dto.getUpdatedAt());
        return entity;
    }
}