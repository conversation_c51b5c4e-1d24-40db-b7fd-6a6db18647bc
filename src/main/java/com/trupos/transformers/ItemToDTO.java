package com.trupos.transformers;

import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.trupos.dtos.DepartmentDTO;
import com.trupos.dtos.ItemDTO;
import com.trupos.entities.Item;

/**
 * Function implementation to transform an Item entity into an ItemDTO
 */
@Component
public class ItemToDTO implements Function<Item, ItemDTO> {
    @Override
    public ItemDTO apply(Item entity) {
        if (entity == null) return null;
        ItemDTO dto = new ItemDTO();
        dto.setId(entity.getId());
        dto.setDescription(entity.getDescription());
        dto.setSku(entity.getSku());
        dto.setUpc(entity.getUpc());
        dto.setBrand(entity.getBrand());
        dto.setSize(entity.getSize());
        dto.setUnitOfMeasure(entity.getUnitOfMeasure());
        dto.setCost(entity.getCost());
        dto.setIsActive(entity.getIsActive());
        dto.setIsTaxable(entity.getIsTaxable());
        dto.setIsFoodStampable(entity.getIsFoodStampable());
        dto.setIsWicEligible(entity.getIsWicEligible());
        dto.setIsAgeRestricted(entity.getIsAgeRestricted());
        dto.setMinimumAge(entity.getMinimumAge());
        dto.setIsReturnable(entity.getIsReturnable());
        dto.setIsScaleItem(entity.getIsScaleItem());
        dto.setIsDepartmentSale(entity.getIsDepartmentSale());
        dto.setTareWeight(entity.getTareWeight());
        dto.setItemImageUrl(entity.getItemImageUrl());
        if (entity.getDepartment() != null) {
            DepartmentDTO d = new DepartmentDTO();
            d.setId(entity.getDepartment().getId());
            dto.setDepartment(d);
        }
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());
        return dto;
    }
}