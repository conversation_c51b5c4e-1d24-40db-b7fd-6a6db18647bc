package com.trupos.transformers;

import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.trupos.dtos.PermissionDTO;
import com.trupos.entities.Permission;

/**
 * Function implementation to transform a PermissionDTO into a Permission entity
 */
@Component
public class PermissionDTOToEntity implements Function<PermissionDTO, Permission> {
    @Override
    public Permission apply(PermissionDTO dto) {
        if (dto == null) return null;
        Permission entity = new Permission();
        entity.setId(dto.getId());
        entity.setName(dto.getName());
        entity.setDescription(dto.getDescription());
        entity.setCreatedAt(dto.getCreatedAt());
        entity.setUpdatedAt(dto.getUpdatedAt());
        return entity;
    }
}