package com.trupos.transformers;

import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.trupos.dtos.TransactionDTO;
import com.trupos.entities.Customer;
import com.trupos.entities.Employee;
import com.trupos.entities.Lane;
import com.trupos.entities.Store;
import com.trupos.entities.Transaction;

/**
 * Function implementation to transform a TransactionDTO into a Transaction entity
 */
@Component
public class TransactionDTOToEntity implements Function<TransactionDTO, Transaction> {
    @Override
    public Transaction apply(TransactionDTO dto) {
        if (dto == null) return null;
        Transaction entity = new Transaction();
        entity.setId(dto.getId());
        if (dto.getStore() != null && dto.getStore().getId() != null) {
            Store s = new Store();
            s.setId(dto.getStore().getId());
            entity.setStore(s);
        }
        if (dto.getLane() != null && dto.getLane().getId() != null) {
            Lane l = new Lane();
            l.setId(dto.getLane().getId());
            entity.setLane(l);
        }
        if (dto.getEmployee() != null && dto.getEmployee().getId() != null) {
            Employee e = new Employee();
            e.setId(dto.getEmployee().getId());
            entity.setEmployee(e);
        }
        if (dto.getCustomer() != null && dto.getCustomer().getId() != null) {
            Customer c = new Customer();
            c.setId(dto.getCustomer().getId());
            entity.setCustomer(c);
        }
        entity.setTimestamp(dto.getTimestamp());
        entity.setStatus(dto.getStatus());
        entity.setSubtotal(dto.getSubtotal());
        entity.setTaxAmount(dto.getTaxAmount());
        entity.setDiscountAmount(dto.getDiscountAmount());
        entity.setTotalAmount(dto.getTotalAmount());
        entity.setItemCount(dto.getItemCount());
        entity.setIsTraining(dto.getIsTraining());
        entity.setIsVoided(dto.getIsVoided());
        entity.setVoidReason(dto.getVoidReason());
        entity.setOriginalTransactionId(dto.getOriginalTransactionId());
        entity.setTransactionData(dto.getTransactionData());
        entity.setCreatedAt(dto.getCreatedAt());
        entity.setUpdatedAt(dto.getUpdatedAt());
        return entity;
    }
}