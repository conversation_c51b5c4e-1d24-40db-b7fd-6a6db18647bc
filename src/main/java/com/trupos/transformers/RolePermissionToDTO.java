package com.trupos.transformers;

import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.trupos.dtos.PermissionDTO;
import com.trupos.dtos.RoleDTO;
import com.trupos.dtos.RolePermissionDTO;
import com.trupos.entities.RolePermission;

/**
 * Function implementation to transform a RolePermission entity into a RolePermissionDTO
 */
@Component
public class RolePermissionToDTO implements Function<RolePermission, RolePermissionDTO> {
    @Override
    public RolePermissionDTO apply(RolePermission entity) {
        if (entity == null) return null;
        RolePermissionDTO dto = new RolePermissionDTO();
        dto.setId(entity.getId());
        dto.setPermissionId(entity.getPermissionId());
        if (entity.getRole() != null) {
            RoleDTO roleDTO = new RoleDTO();
            roleDTO.setId(entity.getRole().getId());
            roleDTO.setName(entity.getRole().getName());
            roleDTO.setCreatedAt(entity.getRole().getCreatedAt());
            roleDTO.setUpdatedAt(entity.getRole().getUpdatedAt());
            dto.setRole(roleDTO);
        }
        if (entity.getPermission() != null) {
            PermissionDTO permissionDTO = new PermissionDTO();
            permissionDTO.setId(entity.getPermission().getId());
            permissionDTO.setName(entity.getPermission().getName());
            permissionDTO.setDescription(entity.getPermission().getDescription());
            permissionDTO.setCreatedAt(entity.getPermission().getCreatedAt());
            permissionDTO.setUpdatedAt(entity.getPermission().getUpdatedAt());
            dto.setPermission(permissionDTO);
        }
        return dto;
    }
}