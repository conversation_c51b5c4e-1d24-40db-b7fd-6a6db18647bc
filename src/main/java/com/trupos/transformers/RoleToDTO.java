package com.trupos.transformers;

import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.trupos.dtos.RoleDTO;
import com.trupos.entities.Role;

/**
 * Function implementation to transform a Role entity into a RoleDTO
 */
@Component
public class RoleToDTO implements Function<Role, RoleDTO> {
    @Override
    public RoleDTO apply(Role entity) {
        if (entity == null) return null;
        RoleDTO dto = new RoleDTO();
        dto.setId(entity.getId());
        dto.setName(entity.getName());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());
        return dto;
    }
}