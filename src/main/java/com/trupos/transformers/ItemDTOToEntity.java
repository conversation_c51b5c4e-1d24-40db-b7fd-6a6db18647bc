package com.trupos.transformers;

import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.trupos.dtos.ItemDTO;
import com.trupos.entities.Department;
import com.trupos.entities.Item;

/**
 * Function implementation to transform an ItemDTO into an Item entity
 */
@Component
public class ItemDTOToEntity implements Function<ItemDTO, Item> {
    @Override
    public Item apply(ItemDTO dto) {
        if (dto == null) return null;
        Item entity = new Item();
        entity.setId(dto.getId());
        entity.setDescription(dto.getDescription());
        entity.setSku(dto.getSku());
        entity.setUpc(dto.getUpc());
        entity.setBrand(dto.getBrand());
        entity.setSize(dto.getSize());
        entity.setUnitOfMeasure(dto.getUnitOfMeasure());
        entity.setCost(dto.getCost());
        entity.setIsActive(dto.getIsActive());
        entity.setIsTaxable(dto.getIsTaxable());
        entity.setIsFoodStampable(dto.getIsFoodStampable());
        entity.setIsWicEligible(dto.getIsWicEligible());
        entity.setIsAgeRestricted(dto.getIsAgeRestricted());
        entity.setMinimumAge(dto.getMinimumAge());
        entity.setIsReturnable(dto.getIsReturnable());
        entity.setIsScaleItem(dto.getIsScaleItem());
        entity.setIsDepartmentSale(dto.getIsDepartmentSale());
        entity.setTareWeight(dto.getTareWeight());
        entity.setItemImageUrl(dto.getItemImageUrl());
        if (dto.getDepartment() != null && dto.getDepartment().getId() != null) {
            Department dept = new Department();
            dept.setId(dto.getDepartment().getId());
            entity.setDepartment(dept);
        }
        entity.setCreatedAt(dto.getCreatedAt());
        entity.setUpdatedAt(dto.getUpdatedAt());
        return entity;
    }
}