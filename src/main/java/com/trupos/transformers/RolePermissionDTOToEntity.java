package com.trupos.transformers;

import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.trupos.dtos.RolePermissionDTO;
import com.trupos.entities.RolePermission;

/**
 * Function implementation to transform a RolePermissionDTO into a RolePermission entity
 */
@Component
public class RolePermissionDTOToEntity implements Function<RolePermissionDTO, RolePermission> {
    @Override
    public RolePermission apply(RolePermissionDTO dto) {
        if (dto == null) return null;
        RolePermission entity = new RolePermission();
        entity.setId(dto.getId());
        entity.setPermissionId(dto.getPermissionId());
        // Role and Permission fields can be set if you want deep population.
        return entity;
    }
}