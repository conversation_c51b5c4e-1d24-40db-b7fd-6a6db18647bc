package com.trupos.transformers;

import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.trupos.dtos.ItemDTO;
import com.trupos.dtos.ItemPriceDTO;
import com.trupos.dtos.StoreDTO;
import com.trupos.entities.ItemPrice;

/**
 * Function implementation to transform an ItemPrice entity into an ItemPriceDTO
 */
@Component
public class ItemPriceToDTO implements Function<ItemPrice, ItemPriceDTO> {
    @Override
    public ItemPriceDTO apply(ItemPrice entity) {
        if (entity == null) return null;
        ItemPriceDTO dto = new ItemPriceDTO();
        dto.setId(entity.getId());
        if (entity.getItem() != null) {
            ItemDTO i = new ItemDTO();
            i.setId(entity.getItem().getId());
            dto.setItem(i);
        }
        if (entity.getStore() != null) {
            StoreDTO s = new StoreDTO();
            s.setId(entity.getStore().getId());
            dto.setStore(s);
        }
        dto.setPrice(entity.getPrice());
        dto.setStartDate(entity.getStartDate());
        dto.setEndDate(entity.getEndDate());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());
        return dto;
    }
}