package com.trupos.transformers;

import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.trupos.dtos.TransactionLineItemDTO;
import com.trupos.entities.Item;
import com.trupos.entities.Transaction;
import com.trupos.entities.TransactionLineItem;

/**
 * Function implementation to transform a TransactionLineItemDTO into a TransactionLineItem entity
 */
@Component
public class TransactionLineItemDTOToEntity implements Function<TransactionLineItemDTO, TransactionLineItem> {
    @Override
    public TransactionLineItem apply(TransactionLineItemDTO dto) {
        if (dto == null) return null;
        TransactionLineItem entity = new TransactionLineItem();
        entity.setId(dto.getId());
        if (dto.getTransaction() != null && dto.getTransaction().getId() != null) {
            Transaction t = new Transaction();
            t.setId(dto.getTransaction().getId());
            entity.setTransaction(t);
        }
        if (dto.getItem() != null && dto.getItem().getId() != null) {
            Item i = new Item();
            i.setId(dto.getItem().getId());
            entity.setItem(i);
        }
        entity.setQuantity(dto.getQuantity());
        entity.setPrice(dto.getPrice());
        entity.setDiscountAmount(dto.getDiscountAmount());
        entity.setTotalAmount(dto.getTotalAmount());
        return entity;
    }
}