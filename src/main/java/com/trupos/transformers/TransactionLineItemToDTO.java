package com.trupos.transformers;

import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.trupos.dtos.ItemDTO;
import com.trupos.dtos.TransactionDTO;
import com.trupos.dtos.TransactionLineItemDTO;
import com.trupos.entities.TransactionLineItem;

/**
 * Function implementation to transform a TransactionLineItem entity into a TransactionLineItemDTO
 */
@Component
public class TransactionLineItemToDTO implements Function<TransactionLineItem, TransactionLineItemDTO> {
    @Override
    public TransactionLineItemDTO apply(TransactionLineItem entity) {
        if (entity == null) return null;
        TransactionLineItemDTO dto = new TransactionLineItemDTO();
        dto.setId(entity.getId());
        if (entity.getTransaction() != null) {
            TransactionDTO t = new TransactionDTO();
            t.setId(entity.getTransaction().getId());
            dto.setTransaction(t);
        }
        if (entity.getItem() != null) {
            ItemDTO i = new ItemDTO();
            i.setId(entity.getItem().getId());
            dto.setItem(i);
        }
        dto.setQuantity(entity.getQuantity());
        dto.setPrice(entity.getPrice());
        dto.setDiscountAmount(entity.getDiscountAmount());
        dto.setTotalAmount(entity.getTotalAmount());
        return dto;
    }
}