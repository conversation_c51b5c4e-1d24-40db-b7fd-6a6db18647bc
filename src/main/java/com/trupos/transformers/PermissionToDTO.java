package com.trupos.transformers;

import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.trupos.dtos.PermissionDTO;
import com.trupos.entities.Permission;

/**
 * Function implementation to transform a Permission entity into a PermissionDTO
 */
@Component
public class PermissionToDTO implements Function<Permission, PermissionDTO> {
    @Override
    public PermissionDTO apply(Permission entity) {
        if (entity == null) return null;
        PermissionDTO dto = new PermissionDTO();
        dto.setId(entity.getId());
        dto.setName(entity.getName());
        dto.setDescription(entity.getDescription());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());
        return dto;
    }
}