package com.trupos.transformers;

import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.trupos.dtos.LaneDTO;
import com.trupos.entities.Lane;
import com.trupos.entities.Store;

/**
 * Function implementation to transform a LaneDTO into a Lane entity
 */
@Component
public class LaneDTOToEntity implements Function<LaneDTO, Lane> {
    @Override
    public Lane apply(LaneDTO dto) {
        if (dto == null) return null;
        Lane entity = new Lane();
        entity.setId(dto.getId());
        entity.setName(dto.getName());
        entity.setNumber(dto.getNumber());
        entity.setIsActive(dto.getIsActive());
        entity.setIsExpress(dto.getIsExpress());
        entity.setMaxItems(dto.getMaxItems());
        entity.setCreatedAt(dto.getCreatedAt());
        entity.setUpdatedAt(dto.getUpdatedAt());
        if (dto.getStore() != null && dto.getStore().getId() != null) {
            Store store = new Store();
            store.setId(dto.getStore().getId());
            entity.setStore(store);
        }
        return entity;
    }
}