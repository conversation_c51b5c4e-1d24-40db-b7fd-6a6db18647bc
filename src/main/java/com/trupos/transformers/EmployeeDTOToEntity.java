package com.trupos.transformers;

import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.trupos.dtos.EmployeeDTO;
import com.trupos.entities.Employee;

/**
 * Function implementation to transform an EmployeeDTO into an Employee entity
 */
@Component
public class EmployeeDTOToEntity implements Function<EmployeeDTO, Employee> {
    @Override
    public Employee apply(EmployeeDTO dto) {
        if (dto == null) return null;
        Employee entity = new Employee();
        entity.setId(dto.getId());
        entity.setFirstName(dto.getFirstName());
        entity.setLastName(dto.getLastName());
        entity.setLoginId(dto.getLoginId());
        entity.setEmail(dto.getEmail());
        entity.setIsActive(dto.getIsActive());
        entity.setStoreId(dto.getStoreId());
        // role is not set via DTO in this pattern
        return entity;
    }
}