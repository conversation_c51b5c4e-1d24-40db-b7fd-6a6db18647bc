package com.trupos.transformers;

import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.trupos.dtos.RoleDTO;
import com.trupos.entities.Role;

/**
 * Function implementation to transform a RoleDTO into a Role entity
 */
@Component
public class RoleDTOToEntity implements Function<RoleDTO, Role> {
    @Override
    public Role apply(RoleDTO dto) {
        if (dto == null) return null;
        Role entity = new Role();
        entity.setId(dto.getId());
        entity.setName(dto.getName());
        entity.setCreatedAt(dto.getCreatedAt());
        entity.setUpdatedAt(dto.getUpdatedAt());
        return entity;
    }
}