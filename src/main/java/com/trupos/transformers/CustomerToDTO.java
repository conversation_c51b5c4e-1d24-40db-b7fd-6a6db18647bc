package com.trupos.transformers;

import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.trupos.dtos.CustomerDTO;
import com.trupos.entities.Customer;

/**
 * Function implementation to transform a Customer entity into a CustomerDTO
 */
@Component
public class CustomerToDTO implements Function<Customer, CustomerDTO> {
    @Override
    public CustomerDTO apply(Customer entity) {
        if (entity == null) return null;
        CustomerDTO dto = new CustomerDTO();
        dto.setId(entity.getId());
        dto.setLoyaltyCardNumber(entity.getLoyaltyCardNumber());
        dto.setFirstName(entity.getFirstName());
        dto.setLastName(entity.getLastName());
        dto.setPhoneNumber(entity.getPhoneNumber());
        dto.setEmail(entity.getEmail());
        dto.setDateOfBirth(entity.getDateOfBirth());
        dto.setIsActive(entity.getIsActive());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());
        return dto;
    }
}