package com.trupos.transformers;

import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.trupos.dtos.CustomerDTO;
import com.trupos.entities.Customer;

/**
 * Function implementation to transform a CustomerDTO into a Customer entity
 */
@Component
public class CustomerDTOToEntity implements Function<CustomerDTO, Customer> {
    @Override
    public Customer apply(CustomerDTO dto) {
        if (dto == null) return null;
        Customer entity = new Customer();
        entity.setId(dto.getId());
        entity.setLoyaltyCardNumber(dto.getLoyaltyCardNumber());
        entity.setFirstName(dto.getFirstName());
        entity.setLastName(dto.getLastName());
        entity.setPhoneNumber(dto.getPhoneNumber());
        entity.setEmail(dto.getEmail());
        entity.setDateOfBirth(dto.getDateOfBirth());
        entity.setIsActive(dto.getIsActive());
        entity.setCreatedAt(dto.getCreatedAt());
        entity.setUpdatedAt(dto.getUpdatedAt());
        return entity;
    }
}