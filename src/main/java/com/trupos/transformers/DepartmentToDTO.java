package com.trupos.transformers;

import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.trupos.dtos.DepartmentDTO;
import com.trupos.entities.Department;

/**
 * Function implementation to transform a Department entity into a DepartmentDTO
 */
@Component
public class DepartmentToDTO implements Function<Department, DepartmentDTO> {
    @Override
    public DepartmentDTO apply(Department entity) {
        if (entity == null) return null;
        DepartmentDTO dto = new DepartmentDTO();
        dto.setId(entity.getId());
        dto.setName(entity.getName());
        dto.setDescription(entity.getDescription());
        dto.setIsActive(entity.getIsActive());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());
        return dto;
    }
}