package com.trupos.transformers;

import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.trupos.dtos.LaneDTO;
import com.trupos.dtos.StoreDTO;
import com.trupos.entities.Lane;

/**
 * Function implementation to transform a Lane entity into a LaneDTO
 */
@Component
public class LaneToDTO implements Function<Lane, LaneDTO> {
    @Override
    public LaneDTO apply(Lane entity) {
        if (entity == null) return null;
        LaneDTO dto = new LaneDTO();
        dto.setId(entity.getId());
        if (entity.getStore() != null) {
            StoreDTO storeDTO = new StoreDTO();
            storeDTO.setId(entity.getStore().getId());
            dto.setStore(storeDTO);
        }
        dto.setName(entity.getName());
        dto.setNumber(entity.getNumber());
        dto.setIsActive(entity.getIsActive());
        dto.setIsExpress(entity.getIsExpress());
        dto.setMaxItems(entity.getMaxItems());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());
        return dto;
    }
}