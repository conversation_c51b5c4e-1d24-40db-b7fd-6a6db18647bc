package com.trupos.dtos;

import java.time.LocalDateTime;
import jakarta.validation.constraints.NotBlank;

import lombok.Data;

/**
 * Data Transfer Object for transferring Department data
 */
@Data
public class DepartmentDTO {

    private Long id;

    @NotBlank(message = "Department name is required")
    private String name;

    @NotBlank(message = "Description is required")
    private String description;

    private Boolean isActive;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}