package com.trupos.dtos;

import java.math.BigDecimal;

import com.trupos.model.TransactionTenderData;

/**
 * DTO for TransactionTenderData
 */
public class TransactionTenderDataDTO {
    private String tenderType;
    private BigDecimal amount;
    private String cardLastFour;
    private String authorizationCode;
    private String referenceNumber;
    private String approvalCode;
    private String currency;
    private Integer tenderSequence;

    // Default constructor
    public TransactionTenderDataDTO() {}

    // Constructor from TransactionTenderData
    public TransactionTenderDataDTO(TransactionTenderData data) {
        this.tenderType = data.getTenderType();
        this.amount = data.getAmount();
        this.cardLastFour = data.getCardLastFour();
        this.authorizationCode = data.getAuthorizationCode();
        this.referenceNumber = data.getReferenceNumber();
        this.approvalCode = data.getApprovalCode();
        this.currency = data.getCurrency();
        this.tenderSequence = data.getTenderSequence();
    }

    // Getters and setters
    public String getTenderType() { return tenderType; }
    public void setTenderType(String tenderType) { this.tenderType = tenderType; }

    public BigDecimal getAmount() { return amount; }
    public void setAmount(BigDecimal amount) { this.amount = amount; }

    public String getCardLastFour() { return cardLastFour; }
    public void setCardLastFour(String cardLastFour) { this.cardLastFour = cardLastFour; }

    public String getAuthorizationCode() { return authorizationCode; }
    public void setAuthorizationCode(String authorizationCode) { this.authorizationCode = authorizationCode; }

    public String getReferenceNumber() { return referenceNumber; }
    public void setReferenceNumber(String referenceNumber) { this.referenceNumber = referenceNumber; }

    public String getApprovalCode() { return approvalCode; }
    public void setApprovalCode(String approvalCode) { this.approvalCode = approvalCode; }

    public String getCurrency() { return currency; }
    public void setCurrency(String currency) { this.currency = currency; }

    public Integer getTenderSequence() { return tenderSequence; }
    public void setTenderSequence(Integer tenderSequence) { this.tenderSequence = tenderSequence; }
}
