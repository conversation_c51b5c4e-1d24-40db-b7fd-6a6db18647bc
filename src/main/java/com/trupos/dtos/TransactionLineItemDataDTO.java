package com.trupos.dtos;

import java.math.BigDecimal;

import com.trupos.model.TransactionLineItemData;

/**
 * DTO for TransactionLineItemData
 */
public class TransactionLineItemDataDTO {
    private Integer lineNumber;
    private Long itemId;
    private String itemDescription;
    private String upc;
    private BigDecimal quantity;
    private BigDecimal unitPrice;
    private BigDecimal extendedPrice;
    private BigDecimal discountAmount;
    private BigDecimal taxAmount;
    private BigDecimal taxRate;
    private Boolean isVoided;
    private String voidReason;
    private BigDecimal weight;
    private String lineItemType;
    private String serialNumber;
    private Integer sequenceNumber;
    private Boolean isTaxable;
    private Boolean isFoodStampable;
    private Boolean isWicEligible;

    // Default constructor
    public TransactionLineItemDataDTO() {}

    // Constructor from TransactionLineItemData
    public TransactionLineItemDataDTO(TransactionLineItemData data) {
        this.lineNumber = data.getLineNumber();
        this.itemId = data.getItemId();
        this.itemDescription = data.getItemDescription();
        this.upc = data.getUpc();
        this.quantity = data.getQuantity();
        this.unitPrice = data.getUnitPrice();
        this.extendedPrice = data.getExtendedPrice();
        this.discountAmount = data.getDiscountAmount();
        this.taxAmount = data.getTaxAmount();
        this.taxRate = data.getTaxRate();
        this.isVoided = data.getIsVoided();
        this.voidReason = data.getVoidReason();
        this.weight = data.getWeight();
        this.lineItemType = data.getLineItemType();
        this.serialNumber = data.getSerialNumber();
        this.sequenceNumber = data.getSequenceNumber();
        this.isTaxable = data.getIsTaxable();
        this.isFoodStampable = data.getIsFoodStampable();
        this.isWicEligible = data.getIsWicEligible();
    }

    // Getters and setters
    public Integer getLineNumber() { return lineNumber; }
    public void setLineNumber(Integer lineNumber) { this.lineNumber = lineNumber; }

    public Long getItemId() { return itemId; }
    public void setItemId(Long itemId) { this.itemId = itemId; }

    public String getItemDescription() { return itemDescription; }
    public void setItemDescription(String itemDescription) { this.itemDescription = itemDescription; }

    public String getUpc() { return upc; }
    public void setUpc(String upc) { this.upc = upc; }

    public BigDecimal getQuantity() { return quantity; }
    public void setQuantity(BigDecimal quantity) { this.quantity = quantity; }

    public BigDecimal getUnitPrice() { return unitPrice; }
    public void setUnitPrice(BigDecimal unitPrice) { this.unitPrice = unitPrice; }

    public BigDecimal getExtendedPrice() { return extendedPrice; }
    public void setExtendedPrice(BigDecimal extendedPrice) { this.extendedPrice = extendedPrice; }

    public BigDecimal getDiscountAmount() { return discountAmount; }
    public void setDiscountAmount(BigDecimal discountAmount) { this.discountAmount = discountAmount; }

    public BigDecimal getTaxAmount() { return taxAmount; }
    public void setTaxAmount(BigDecimal taxAmount) { this.taxAmount = taxAmount; }

    public BigDecimal getTaxRate() { return taxRate; }
    public void setTaxRate(BigDecimal taxRate) { this.taxRate = taxRate; }

    public Boolean getIsVoided() { return isVoided; }
    public void setIsVoided(Boolean isVoided) { this.isVoided = isVoided; }

    public String getVoidReason() { return voidReason; }
    public void setVoidReason(String voidReason) { this.voidReason = voidReason; }

    public BigDecimal getWeight() { return weight; }
    public void setWeight(BigDecimal weight) { this.weight = weight; }

    public String getLineItemType() { return lineItemType; }
    public void setLineItemType(String lineItemType) { this.lineItemType = lineItemType; }

    public String getSerialNumber() { return serialNumber; }
    public void setSerialNumber(String serialNumber) { this.serialNumber = serialNumber; }

    public Integer getSequenceNumber() { return sequenceNumber; }
    public void setSequenceNumber(Integer sequenceNumber) { this.sequenceNumber = sequenceNumber; }

    public Boolean getIsTaxable() { return isTaxable; }
    public void setIsTaxable(Boolean isTaxable) { this.isTaxable = isTaxable; }

    public Boolean getIsFoodStampable() { return isFoodStampable; }
    public void setIsFoodStampable(Boolean isFoodStampable) { this.isFoodStampable = isFoodStampable; }

    public Boolean getIsWicEligible() { return isWicEligible; }
    public void setIsWicEligible(Boolean isWicEligible) { this.isWicEligible = isWicEligible; }
}
