package com.trupos.dtos;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import lombok.Data;

/**
 * Data Transfer Object for transferring Item data
 */
@Data
public class ItemDTO {
    private Long id;
    private String description;
    private String sku;
    private String upc;
    private String brand;
    private String size;
    private String unitOfMeasure;
    private BigDecimal cost;
    private Boolean isActive;
    private Boolean isTaxable;
    private Boolean isFoodStampable;
    private Boolean isWicEligible;
    private Boolean isAgeRestricted;
    private Integer minimumAge;
    private Boolean isReturnable;
    private Boolean isScaleItem;
    private Boolean isDepartmentSale;
    private BigDecimal tareWeight;
    private String itemImageUrl;
    private DepartmentDTO department;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}