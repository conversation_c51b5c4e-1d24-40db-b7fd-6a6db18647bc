package com.trupos.dtos;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import lombok.Data;

/**
 * Data Transfer Object for transferring Store data
 */
@Data
public class StoreDTO {
    private Long id;
    private String name;
    private String address;
    private String city;
    private String state;
    private String zipCode;
    private String phoneNumber;
    private BigDecimal taxRate;
    private Integer storeNo;
    private Integer wicStateId;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}