package com.trupos.dtos;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;

import lombok.Data;

/**
 * Data Transfer Object for transferring Store data
 */
@Data
public class StoreDTO {
    private Long id;

    @NotBlank(message = "Store name is required")
    private String name;

    private String address;
    private String city;
    private String state;
    private String zipCode;
    private String phoneNumber;

    @Positive(message = "Tax rate must be positive")
    private BigDecimal taxRate;

    @NotNull(message = "Store number is required")
    private Integer storeNo;

    private Integer wicStateId;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}