package com.trupos.dtos;

import java.time.LocalDateTime;

import lombok.Data;

/**
 * Data Transfer Object for transferring Lane data
 */
@Data
public class LaneDTO {
    private Long id;
    private StoreDTO store;
    private String name;
    private Integer number;
    private Boolean isActive;
    private Boolean isExpress;
    private Integer maxItems;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}