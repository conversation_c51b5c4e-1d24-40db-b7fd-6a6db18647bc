package com.trupos.dtos;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import lombok.Data;

/**
 * Data Transfer Object for transferring ItemPrice data
 */
@Data
public class ItemPriceDTO {
    private Long id;
    private ItemDTO item;
    private StoreDTO store;
    private BigDecimal price;
    private LocalDateTime startDate;
    private LocalDateTime endDate;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}