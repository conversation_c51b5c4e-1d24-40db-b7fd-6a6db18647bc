package com.trupos.dtos;

import java.math.BigDecimal;

import lombok.Data;

/**
 * Data Transfer Object for transferring TransactionLineItem data
 */
@Data
public class TransactionLineItemDTO {
    private Long id;
    private TransactionDTO transaction;
    private ItemDTO item;
    private Integer quantity;
    private BigDecimal price;
    private BigDecimal discountAmount;
    private BigDecimal totalAmount;
}