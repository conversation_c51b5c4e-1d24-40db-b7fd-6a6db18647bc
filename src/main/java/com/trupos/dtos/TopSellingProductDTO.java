package com.trupos.dtos;

import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * DTO for top selling products report
 */
public class TopSellingProductDTO {
    
    @JsonProperty("product_id")
    private Long productId;
    
    @JsonProperty("product_name")
    private String productName;
    
    @JsonProperty("total_quantity")
    private BigDecimal totalQuantity;
    
    @JsonProperty("total_sales")
    private BigDecimal totalSales;
    
    // Default constructor
    public TopSellingProductDTO() {
    }
    
    // Constructor
    public TopSellingProductDTO(Long productId, String productName, BigDecimal totalQuantity, BigDecimal totalSales) {
        this.productId = productId;
        this.productName = productName;
        this.totalQuantity = totalQuantity;
        this.totalSales = totalSales;
    }
    
    // Getters and setters
    public Long getProductId() {
        return productId;
    }
    
    public void setProductId(Long productId) {
        this.productId = productId;
    }
    
    public String getProductName() {
        return productName;
    }
    
    public void setProductName(String productName) {
        this.productName = productName;
    }
    
    public BigDecimal getTotalQuantity() {
        return totalQuantity;
    }
    
    public void setTotalQuantity(BigDecimal totalQuantity) {
        this.totalQuantity = totalQuantity;
    }
    
    public BigDecimal getTotalSales() {
        return totalSales;
    }
    
    public void setTotalSales(BigDecimal totalSales) {
        this.totalSales = totalSales;
    }
    
    @Override
    public String toString() {
        return "TopSellingProductDTO{" +
                "productId=" + productId +
                ", productName='" + productName + '\'' +
                ", totalQuantity=" + totalQuantity +
                ", totalSales=" + totalSales +
                '}';
    }
}
