package com.trupos.dtos;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import lombok.Data;

/**
 * Data Transfer Object for transferring Transaction data
 */
@Data
public class TransactionDTO {
    private Long id;
    private StoreDTO store;
    private LaneDTO lane;
    private EmployeeDTO employee;
    private CustomerDTO customer;
    private LocalDateTime timestamp;
    private String status;
    private BigDecimal subtotal;
    private BigDecimal taxAmount;
    private BigDecimal discountAmount;
    private BigDecimal totalAmount;
    private Integer itemCount;
    private Boolean isTraining;
    private Boolean isVoided;
    private String voidReason;
    private Long originalTransactionId;
    private String transactionData;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}