package com.trupos.entities;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Entity definition for the item table
 */
@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "item")
public class Item {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "item_id")
    private Long id;

    @Column(name = "description", nullable = false, length = 255)
    private String description;

    @Column(name = "sku", length = 50)
    private String sku;

    @Column(name = "upc", length = 20)
    private String upc;

    @Column(name = "brand", length = 255)
    private String brand;

    @Column(name = "size", length = 100)
    private String size;

    @Column(name = "unit_of_measure", length = 10)
    private String unitOfMeasure = "EA";

    @Column(name = "cost", precision = 10, scale = 2)
    private BigDecimal cost;

    @Column(name = "is_active")
    private Boolean isActive = true;

    @Column(name = "is_taxable")
    private Boolean isTaxable = true;

    @Column(name = "is_food_stampable")
    private Boolean isFoodStampable = false;

    @Column(name = "is_wic_eligible")
    private Boolean isWicEligible = false;

    @Column(name = "is_age_restricted")
    private Boolean isAgeRestricted = false;

    @Column(name = "minimum_age")
    private Integer minimumAge = 0;

    @Column(name = "is_returnable")
    private Boolean isReturnable = true;

    @Column(name = "is_scale_item")
    private Boolean isScaleItem = false;

    @Column(name = "is_department_sale")
    private Boolean isDepartmentSale = false;

    @Column(name = "tare_weight", precision = 8, scale = 3)
    private BigDecimal tareWeight;

    @Column(name = "item_image_url", length = 255)
    private String itemImageUrl;

    @ManyToOne
    @JoinColumn(name = "department_id")
    private Department department;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt = LocalDateTime.now();

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
}