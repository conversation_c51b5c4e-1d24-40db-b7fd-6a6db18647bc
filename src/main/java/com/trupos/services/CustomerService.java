package com.trupos.services;

import java.util.List;
import java.util.stream.Collectors;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.trupos.dtos.CustomerDTO;
import com.trupos.entities.Customer;
import com.trupos.exceptions.custom.DuplicateEmailException;
import com.trupos.exceptions.custom.DuplicateLoyaltyCardException;
import com.trupos.exceptions.custom.DuplicatePhoneNumberException;
import com.trupos.exceptions.custom.NotFoundException;
import com.trupos.repositories.CustomerRepository;
import com.trupos.transformers.CustomerDTOToEntity;
import com.trupos.transformers.CustomerToDTO;

/**
 * Service implementation to handle customer operations
 */
@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class CustomerService {

    private final CustomerToDTO toDTO;
    private final CustomerDTOToEntity toEntity;
    private final CustomerRepository customerRepository;

    /**
     * Saves a new customer
     *
     * @param dto CustomerDTO
     * @return CustomerDTO
     * @throws DuplicateEmailException if email already exists
     * @throws DuplicatePhoneNumberException if phone number already exists
     * @throws DuplicateLoyaltyCardException if loyalty card number already exists
     */
    public CustomerDTO save(CustomerDTO dto) {

        log.info("save() -> dto: {}", dto);

        // Convert DTO to Entity
        Customer customer = toEntity.apply(dto);

        // Delegate duplicate entry validation logic to helper method
        validateDuplicates(customer, null);

        // Save new customer to DB
        Customer saved = customerRepository.save(customer);

        // Convert back to DTO and return
        return toDTO.apply(saved);
    }

    /**
     * Finds a customer by their ID
     *
     * @param id Long
     * @return Optional<CustomerDTO>
     * @throws NotFoundException if customer does not exist
     */
    public CustomerDTO findById(Long id) {

        // Find customer by given ID, convert to DTO if found, throw NotFoundException if the customer does not exist
        return customerRepository.findById(id)
                .map(toDTO)
                .orElseThrow(() -> new NotFoundException("Customer not found with id: " + id));
    }

    /**
     * Updates an existing customer
     *
     * @param id Long
     * @param dto CustomerDTO
     * @return CustomerDTO
     * @throws NotFoundException if customer does not exist
     * @throws DuplicateEmailException if email already exists
     * @throws DuplicateLoyaltyCardException if loyalty card number already exists
     */
    public CustomerDTO update(Long id, CustomerDTO dto) {

        log.info("update() -> id: {}, dto: {}", id, dto);

        // Ensure the customer exists before updating
        Customer found = customerRepository.findById(id)
                .orElseThrow(() -> new NotFoundException("Customer not found with id: " + id));

        // Assign the ID to the DTO to prevent mismatched updates
        dto.setId(id);

        // Convert DTO to entity
        Customer updated = toEntity.apply(dto);

        // Delegate duplicate entry validation logic to helper method
        validateDuplicates(updated, id);

        // Save updated customer to DB
        Customer saved = customerRepository.save(updated);

        // Convert back to DTO and return
        return toDTO.apply(saved);
    }

    /**
     * Deactivates a customer
     *
     * @param id Long
     * @return CustomerDTO
     * @throws NotFoundException if customer does not exist
     */
    public CustomerDTO deactivate(Long id) {

        // Delegate action to changeActiveStatus and return customer
        return changeActiveStatus(id, false);
    }

    /**
     * Activates a customer
     *
     * @param id Long
     * @return CustomerDTO
     * @throws NotFoundException if customer does not exist
     */
    public CustomerDTO activate(Long id) {

        // Delegate action to changeActiveStatus and return customer
        return changeActiveStatus(id, true);
    }

    /**
     * Deletes a customer
     *
     * @param id Long
     * @throws NotFoundException if customer does not exist
     */
    public void delete(Long id) {

        // Find customer by given ID, throw NotFoundException if the customer does not exist
        Customer found = customerRepository.findById(id)
                .orElseThrow(() -> new NotFoundException("Customer not found with id: " + id));

        // Delete found customer
        customerRepository.delete(found);
    }

    /**
     * Finds a customer by their loyalty card number
     *
     * @param loyaltyCardNumber String
     * @return Optional<CustomerDTO>
     * @throws NotFoundException if customer does not exist
     */
    public CustomerDTO findByLoyaltyCard(String loyaltyCardNumber) {

        // Find customer by given loyalty card number, convert to DTO if found, throw NotFoundException if the customer does not exist
        return customerRepository.findByLoyaltyCardNumber(loyaltyCardNumber)
                .map(toDTO)
                .orElseThrow(() -> new NotFoundException("Customer not found with loyalty card number: " + loyaltyCardNumber));
    }

    /**
     * Finds a customer by their email
     *
     * @param email String
     * @return CustomerDTO
     * @throws NotFoundException if customer does not exist
     */
    public CustomerDTO findByEmail(String email) {

        // Find customer by given email, convert to DTO if found, throw NotFoundException if the customer does not exist
        return customerRepository.findByEmail(email)
                .map(toDTO)
                .orElseThrow(() -> new NotFoundException("Customer not found with email: " + email));
    }

    /**
     * Finds customers by partial phone number
     *
     * @param phoneNumber String
     * @return List<CustomerDTO>
     */
    public List<CustomerDTO> findByPhone(String phoneNumber) {

        // Find customer(s) by given phone number and convert to DTO(s) if found
        return customerRepository.findByPhoneNumberContaining(phoneNumber)
                .stream()
                .map(toDTO)
                .collect(Collectors.toList());
    }

    /**
     * Finds all customers currently marked as active
     *
     * @return List<CustomerDTO>
     */
    public List<CustomerDTO> findActive() {

        // Find active customer(s) and convert to DTO(s) if found
        return customerRepository.findByIsActiveTrue()
                .stream()
                .map(toDTO)
                .collect(Collectors.toList());
    }

    /**
     * Searches customers by a search term with pagination
     *
     * @param searchTerm String
     * @param pageable Pageable
     * @return Page<CustomerDTO>
     */
    public Page<CustomerDTO> search(String searchTerm, Pageable pageable) {

        // Search for customer(s) and convert to DTO(s) if found
        return customerRepository.search(searchTerm, pageable).map(toDTO);
    }

    /**
     * Changes the active status of a customer
     *
     * @param id Long
     * @param isActive boolean
     * @return CustomerDTO
     * @throws NotFoundException if customer does not exist
     */
    private CustomerDTO changeActiveStatus(Long id, boolean isActive) {

        // Find customer by given ID, throw NotFoundException if the customer does not exist.
        Customer found = customerRepository.findById(id)
                .orElseThrow(() -> new NotFoundException("Customer not found with id: " + id));

        // Set isActive to passed value
        found.setIsActive(isActive);

        // Save updated customer to DB
        Customer saved = customerRepository.save(found);

        // Convert to DTO and return
        return toDTO.apply(saved);
    }

    /**
     * Validates uniqueness of loyalty card number, email, and phone number for a customer
     *
     * @param customer Customer
     * @param excludeId Long
     * @throws DuplicateEmailException if email already exists
     * @throws DuplicatePhoneNumberException if phone number already exists
     * @throws DuplicateLoyaltyCardException if loyalty card number already exists
     */
    private void validateDuplicates(Customer customer, Long excludeId) {

        // Check for duplicate loyalty card number if provided
        // Exclude customer from being updated (if applicable)
        // Throw DuplicateLoyaltyCardException if duplicate found
        if (customer.getLoyaltyCardNumber() != null) {
            customerRepository.findByLoyaltyCardNumber(customer.getLoyaltyCardNumber())
                    .filter(existing -> !existing.getId().equals(excludeId))
                    .ifPresent(c -> { throw new DuplicateLoyaltyCardException("Loyalty card number already exists"); });
        }

        // Check for duplicate email if provided
        // Exclude customer from being updated (if applicable)
        // Throw DuplicateEmailException if duplicate found
        if (customer.getEmail() != null) {
            customerRepository.findByEmail(customer.getEmail())
                    .filter(existing -> !existing.getId().equals(excludeId))
                    .ifPresent(c -> { throw new DuplicateEmailException("Email already exists"); });
        }

        // Check for duplicate phone number if provided
        // Exclude customer from being updated (if applicable)
        // Throw DuplicatePhoneNumberException if duplicate found
        if (customer.getPhoneNumber() != null) {
            customerRepository.findByPhoneNumber(customer.getPhoneNumber())
                    .filter(existing -> !existing.getId().equals(excludeId))
                    .ifPresent(c -> { throw new DuplicatePhoneNumberException("Phone number already exists"); });
        }
    }
}