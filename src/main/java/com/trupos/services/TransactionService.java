package com.trupos.services;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.trupos.dtos.TopSellingProductDTO;
import com.trupos.entities.Employee;
import com.trupos.entities.Item;
import com.trupos.entities.Lane;
import com.trupos.entities.Store;
import com.trupos.entities.Transaction;
import com.trupos.models.TransactionDocument;
import com.trupos.models.TransactionLineItemData;
import com.trupos.models.TransactionTenderData;
import com.trupos.repositories.CustomerRepository;
import com.trupos.repositories.EmployeeRepository;
import com.trupos.repositories.ItemRepository;
import com.trupos.repositories.LaneRepository;
import com.trupos.repositories.StoreRepository;
import com.trupos.repositories.TransactionRepository;

/**
 * Service implementation to handle transaction operations
 */
@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class TransactionService {

    private final TransactionRepository transactionRepository;
    private final StoreRepository storeRepository;
    private final LaneRepository laneRepository;
    private final EmployeeRepository employeeRepository;
    private final CustomerRepository customerRepository;
    private final ItemRepository itemRepository;
    private final TransactionDocumentService documentService;

    /**
     * Create a new transaction using hybrid document approach
     *
     * @param storeId Long
     * @param laneId Long
     * @param employeeId Long
     * @param customerId Long
     * @return Transaction
     */
    public Transaction createTransaction(Long storeId, Long laneId, Long employeeId, Long customerId) {
        // Validate required entities exist
        Store store = storeRepository.findById(storeId)
                .orElseThrow(() -> new RuntimeException("Store not found with id: " + storeId));

        Lane lane = laneRepository.findById(laneId)
                .orElseThrow(() -> new RuntimeException("Lane not found with id: " + laneId));

        Employee employee = employeeRepository.findById(employeeId)
                .orElseThrow(() -> new RuntimeException("Employee not found with id: " + employeeId));

        if (customerId != null) {
            customerRepository.findById(customerId)
                    .orElseThrow(() -> new RuntimeException("Customer not found with id: " + customerId));
        }

        // Create transaction with hybrid approach
        Transaction transaction = new Transaction(storeId, laneId, employeeId, customerId);

        // Create and set the transaction document
        TransactionDocument document = documentService.createNewTransactionDocument(storeId, laneId, employeeId, customerId);
        document = documentService.enrichTransactionDocument(transaction);
        transaction.setTransactionDocument(document);

        // Save the transaction to the database
        return transactionRepository.save(transaction);
    }

    /**
     * Add line item to transaction using document approach
     *
     * @param transactionId Long
     * @param itemId Long
     * @param quantity BigDecimal
     * @param unitPrice BigDecimal
     * @return TransactionLineItemData
     */
    public TransactionLineItemData addLineItem(Long transactionId, Long itemId, BigDecimal quantity, BigDecimal unitPrice) {
        // Retrieve transaction from the database
        Transaction transaction = transactionRepository.findById(transactionId)
                .orElseThrow(() -> new RuntimeException("Transaction not found with id: " + transactionId));

        // Validate transaction status allows modifications
        if (transaction.getStatus() != Transaction.TransactionStatus.OPEN &&
                transaction.getStatus() != Transaction.TransactionStatus.SUSPENDED) {
            throw new IllegalStateException("Cannot add line items to transaction in " + transaction.getStatus() + " state");
        }

        // Retrieve item from the database
        Item item = itemRepository.findById(itemId)
                .orElseThrow(() -> new RuntimeException("Item not found with id: " + itemId));

        // Get transaction document
        TransactionDocument document = transaction.getTransactionDocument();

        // Get next line number
        int nextLineNumber = document.getLineItems().size() + 1;

        // Create line item data
        TransactionLineItemData lineItemData = new TransactionLineItemData(nextLineNumber, itemId, item.getDescription(), quantity, unitPrice);
        lineItemData.setUpc(item.getUpc());
        lineItemData.setIsTaxable(item.getIsTaxable());
        lineItemData.setSequenceNumber(nextLineNumber);

        // Calculate tax if item is taxable
        if (item.getIsTaxable() && document.getStoreInfo() != null && document.getStoreInfo().getTaxRate() != null) {
            BigDecimal taxRate = document.getStoreInfo().getTaxRate();
            BigDecimal taxAmount = lineItemData.getExtendedPrice().multiply(taxRate).setScale(2, RoundingMode.HALF_UP);
            lineItemData.setTaxAmount(taxAmount);
            lineItemData.setTaxRate(taxRate);
        }

        // Add line item to document
        document.getLineItems().add(lineItemData);

        // Recalculate transaction totals in document
        documentService.updateTransactionTotals(document);

        // Update transaction with new document
        transaction.setTransactionDocument(document);

        // Save transaction
        transactionRepository.save(transaction);

        return lineItemData;
    }

    /**
     * Add tender to transaction
     *
     * @param transactionId Long
     * @param tenderType String
     * @param amount BigDecimal
     * @param cardLastFour String
     * @param authorizationCode String
     * @return TransactionTenderData
     */
    public TransactionTenderData addTender(Long transactionId, String tenderType,
                                           BigDecimal amount, String cardLastFour, String authorizationCode) {
        // Retrieve transaction from the database
        Transaction transaction = transactionRepository.findById(transactionId)
                .orElseThrow(() -> new RuntimeException("Transaction not found with id: " + transactionId));

        // Validate transaction status allows modifications
        if (transaction.getStatus() != Transaction.TransactionStatus.OPEN &&
                transaction.getStatus() != Transaction.TransactionStatus.SUSPENDED) {
            throw new IllegalStateException("Cannot add tender to transaction in " + transaction.getStatus() + " state");
        }

        // Create tender data
        TransactionTenderData tenderData = new TransactionTenderData(tenderType, amount, cardLastFour, authorizationCode);

        // Get transaction document and add tender
        TransactionDocument document = transaction.getTransactionDocument();
        tenderData.setTenderSequence(document.getTenders().size() + 1);

        List<TransactionTenderData> tenders = new ArrayList<>(document.getTenders());
        tenders.add(tenderData);
        document.setTenders(tenders);

        // Update transaction with new document
        transaction.setTransactionDocument(document);

        // Save transaction
        transactionRepository.save(transaction);

        return tenderData;
    }

    /**
     * Complete transaction using document approach
     *
     * @param transactionId Long
     * @return Transaction
     */
    public Transaction completeTransaction(Long transactionId) {
        // Retrieve transaction from the database
        Transaction transaction = transactionRepository.findById(transactionId)
                .orElseThrow(() -> new RuntimeException("Transaction not found with id: " + transactionId));

        // Get transaction document
        TransactionDocument document = transaction.getTransactionDocument();

        // Validate transaction can be completed
        if (document.getLineItems().isEmpty()) {
            throw new IllegalStateException("Cannot complete transaction with no line items");
        }

        // Calculate total tendered amount
        BigDecimal totalTendered = document.getTenders().stream()
                .map(TransactionTenderData::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (totalTendered.compareTo(document.getTotalAmount()) < 0) {
            throw new IllegalStateException("Insufficient payment amount");
        }

        // Update document status and completion time
        document.setStatus(Transaction.TransactionStatus.COMPLETED);
        document.setCompletedAt(LocalDateTime.now());

        // Enrich document with final data for archival
        document = documentService.enrichTransactionDocument(transaction);
        document.setStatus(Transaction.TransactionStatus.COMPLETED);
        document.setCompletedAt(LocalDateTime.now());

        // Update transaction with completed document
        transaction.setTransactionDocument(document);

        // Save the completed transaction to the database
        return transactionRepository.save(transaction);
    }

    /**
     * Suspend transaction
     *
     * @param transactionId Long
     * @return Transaction
     */
    public Transaction suspendTransaction(Long transactionId) {
        // Retrieve transaction from the database
        Transaction transaction = transactionRepository.findById(transactionId)
                .orElseThrow(() -> new RuntimeException("Transaction not found with id: " + transactionId));

        // Update transacion status as suspended & save
        transaction.setStatus(Transaction.TransactionStatus.SUSPENDED);
        return transactionRepository.save(transaction);
    }

    /**
     * Resume suspended transaction
     *
     * @param transactionId Long
     * @return Transaction
     */
    public Transaction resumeTransaction(Long transactionId) {
        // Retrieve transaction from the database
        Transaction transaction = transactionRepository.findById(transactionId)
                .orElseThrow(() -> new RuntimeException("Transaction not found with id: " + transactionId));

        // Validate that the transaction is in suspended state
        if (transaction.getStatus() != Transaction.TransactionStatus.SUSPENDED) {
            throw new IllegalStateException("Transaction is not suspended");
        }

        // Update transaction status as open & save
        transaction.setStatus(Transaction.TransactionStatus.OPEN);
        return transactionRepository.save(transaction);
    }

    /**
     * Void transaction
     *
     * @param transactionId Long
     * @param voidReason String
     * @return Transaction
     */
    public Transaction voidTransaction(Long transactionId, String voidReason) {
        // Retrieve transaction from the database
        Transaction transaction = transactionRepository.findById(transactionId)
                .orElseThrow(() -> new RuntimeException("Transaction not found with id: " + transactionId));

        // Update transaction status as voided & save
        transaction.setStatus(Transaction.TransactionStatus.VOIDED);
        transaction.setIsVoided(true);
        transaction.setVoidReason(voidReason);

        return transactionRepository.save(transaction);
    }

    /**
     * Update transaction customer
     *
     * @param transactionId Long
     * @param customerId Long
     * @return Transaction
     */
    public Transaction updateTransactionCustomer(Long transactionId, Long customerId) {
        // Retrieve transaction from the database
        Transaction transaction = transactionRepository.findById(transactionId)
                .orElseThrow(() -> new RuntimeException("Transaction not found with id: " + transactionId));

        // Validate transaction status allows modifications
        if (transaction.getStatus() != Transaction.TransactionStatus.OPEN &&
                transaction.getStatus() != Transaction.TransactionStatus.SUSPENDED) {
            throw new IllegalStateException("Cannot update customer for transaction in " + transaction.getStatus() + " state");
        }

        // Validate customer exists if provided
        if (customerId != null) {
            customerRepository.findById(customerId)
                    .orElseThrow(() -> new RuntimeException("Customer not found with id: " + customerId));
        }

        // Update transaction customer
        transaction.setCustomerId(customerId);

        // Update transaction document customer info
        TransactionDocument document = transaction.getTransactionDocument();
        document.setCustomerId(customerId);
        transaction.setTransactionDocument(document);

        return transactionRepository.save(transaction);
    }

    /**
     * Void line item by sequence number
     *
     * @param transactionId Long
     * @param lineNumber Integer
     * @param voidReason String
     */
    public void voidLineItem(Long transactionId, Integer lineNumber, String voidReason) {
        // Retrieve transaction from the database
        Transaction transaction = transactionRepository.findById(transactionId)
                .orElseThrow(() -> new RuntimeException("Transaction not found with id: " + transactionId));

        // Validate transaction status allows modifications
        if (transaction.getStatus() != Transaction.TransactionStatus.OPEN &&
                transaction.getStatus() != Transaction.TransactionStatus.SUSPENDED) {
            throw new IllegalStateException("Cannot void line items in transaction in " + transaction.getStatus() + " state");
        }

        // Get transaction document and modify line items
        TransactionDocument document = transaction.getTransactionDocument();
        List<TransactionLineItemData> lineItems = new ArrayList<>(document.getLineItems());

        // Loop through the line items to check see if the correct item is present and mark as void
        for (TransactionLineItemData lineItem : lineItems) {
            if (lineItem.getLineNumber().equals(lineNumber)) {
                lineItem.setIsVoided(true);
                lineItem.setVoidReason(voidReason);
                break;
            }
        }

        // Update document with modified line items
        document.setLineItems(lineItems);
        transaction.setTransactionDocument(document);

        // Recalculate transaction totals
        recalculateTransactionTotals(transaction);

        // Save transaction
        transactionRepository.save(transaction);
    }

    /**
     * Get transaction by ID
     *
     * @param transactionId Long
     * @return Optional<Transaction>
     */
    @Transactional(readOnly = true)
    public Optional<Transaction> getTransactionById(Long transactionId) {
        // Retrieve transaction by ID from the databse
        return transactionRepository.findById(transactionId);
    }

    /**
     * Get transactions by store with pagination
     *
     * @param storeId Long
     * @param pageable Pageable
     * @return Page<Transaction>
     */
    @Transactional(readOnly = true)
    public Page<Transaction> getTransactionsByStore(Long storeId, Pageable pageable) {
        // Retrieve store associated with the ID provided
        Store store = storeRepository.findById(storeId)
                .orElseThrow(() -> new RuntimeException("Store not found with id: " + storeId));

        // Retrieve all transactions associated with the store & sort
        return transactionRepository.findByStoreOrderByTransactionTimestampDesc(store, pageable);
    }

    /**
     * Get completed transactions by store with pagination
     *
     * @param storeId Long
     * @param pageable Pageable
     * @return Page<Transaction>
     */
    @Transactional(readOnly = true)
    public Page<Transaction> getCompletedTransactionsByStore(Long storeId, Pageable pageable) {
        // Retrieve store associated with the ID provided
        Store store = storeRepository.findById(storeId)
                .orElseThrow(() -> new RuntimeException("Store not found with id: " + storeId));

        // Retrieve all transactions associated with the store & sort by time
        return transactionRepository.findByStoreAndStatusOrderByTransactionTimestampDesc(store, Transaction.TransactionStatus.COMPLETED, pageable);
    }

    /**
     * Get suspended transactions by lane
     *
     * @param laneId Long
     * @return List<Transaction>
     */
    @Transactional(readOnly = true)
    public List<Transaction> getSuspendedTransactionsByLane(Long laneId) {
        // Retrieve transaction by lane ID
        Lane lane = laneRepository.findById(laneId)
                .orElseThrow(() -> new RuntimeException("Lane not found with id: " + laneId));

        return transactionRepository.findSuspendedTransactionsByLane(lane);
    }

    /**
     * Calculate sales totals for date range
     *
     * @param storeId Long
     * @param startDate LocalDateTime
     * @param endDate LocalDateTime
     * @return BigDecimal
     */
    @Transactional(readOnly = true)
    public BigDecimal calculateTotalSales(Long storeId, LocalDateTime startDate, LocalDateTime endDate) {
        // Retrieve transaction by Store ID
        Store store = storeRepository.findById(storeId)
                .orElseThrow(() -> new RuntimeException("Store not found with id: " + storeId));

        // Calculate the total sales within a provided date range
        return transactionRepository.calculateTotalSales(store, startDate, endDate);
    }

    /**
     * Get top selling products for date range
     *
     * @param storeId Long
     * @param startDate LocalDateTime
     * @param endDate LocalDateTime
     * @param limit int
     * @return List<TopSellingProductDTO>
     */
    @Transactional(readOnly = true)
    public List<TopSellingProductDTO> getTopSellingProducts(Long storeId, LocalDateTime startDate, LocalDateTime endDate, int limit) {
        // Get store reference
        Store store = storeRepository.findById(storeId)
                .orElseThrow(() -> new RuntimeException("Store not found with id: " + storeId));

        // Get completed transactions in date range
        List<Transaction> transactions = transactionRepository.findByStoreAndDateRange(store, startDate, endDate)
                .stream()
                .filter(t -> t.getStatus() == Transaction.TransactionStatus.COMPLETED && !t.getIsVoided())
                .collect(Collectors.toList());

        // Map to track product sales
        Map<Long, TopSellingProductDTO> productSalesMap = new HashMap<>();

        // Loop through transactions to aggregate product sales
        for (Transaction transaction : transactions) {
            List<TransactionLineItemData> lineItems = transaction.getLineItems();

            // Aggregate to product sales map
            for (TransactionLineItemData lineItem : lineItems) {
                if (!lineItem.getIsVoided()) {
                    Long itemId = lineItem.getItemId();

                    TopSellingProductDTO productSales = productSalesMap.computeIfAbsent(itemId, id -> {
                        // Get item details
                        Optional<Item> item = itemRepository.findById(id);
                        return new TopSellingProductDTO(
                                id,
                                item.map(Item::getDescription).orElse("Unknown Product"),
                                BigDecimal.ZERO,
                                BigDecimal.ZERO
                        );
                    });

                    // Add to totals
                    productSales.setTotalQuantity(productSales.getTotalQuantity().add(lineItem.getQuantity()));
                    productSales.setTotalSales(productSales.getTotalSales().add(lineItem.getExtendedPrice()));
                }
            }
        }

        // Convert to list, sort by total sales, and limit results
        return productSalesMap.values().stream()
                .sorted((a, b) -> b.getTotalSales().compareTo(a.getTotalSales()))
                .limit(limit)
                .collect(Collectors.toList());
    }

    /**
     * Recalculate transaction totals
     *
     * @param transaction Transaction
     */
    private void recalculateTransactionTotals(Transaction transaction) {
        // Get transaction document and recalculate totals
        TransactionDocument document = transaction.getTransactionDocument();

        // Use the document service to update totals
        documentService.updateTransactionTotals(document);

        // Update transaction with recalculated document
        transaction.setTransactionDocument(document);
    }
}