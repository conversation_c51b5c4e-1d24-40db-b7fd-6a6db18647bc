package com.trupos.services;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.trupos.entities.Item;
import com.trupos.entities.ItemPrice;
import com.trupos.entities.Store;
import com.trupos.repositories.DepartmentRepository;
import com.trupos.repositories.ItemPriceRepository;
import com.trupos.repositories.ItemRepository;
import com.trupos.repositories.StoreRepository;

/**
 * Service implementation to handle product operations
 */
@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class ProductService {

    private final ItemRepository itemRepository;
    private final ItemPriceRepository itemPriceRepository;
    private final DepartmentRepository departmentRepository;
    private final StoreRepository storeRepository;

    /**
     * Create a new product (item)
     *
     * @param item Item
     * @return Item
     */
    public Item createProduct(Item item) {
        // Validate required fields
        if (item.getDescription() == null || item.getDescription().trim().isEmpty()) {
            throw new IllegalArgumentException("Product description is required");
        }

        // Check for duplicate UPC
        if (item.getUpc() != null && itemRepository.findByUpc(item.getUpc()).isPresent()) {
            throw new IllegalArgumentException("UPC already exists");
        }

        // Check for duplicate SKU
        if (item.getSku() != null && itemRepository.findBySku(item.getSku()).isPresent()) {
            throw new IllegalArgumentException("SKU already exists");
        }

        // Save the new product to the database
        return itemRepository.save(item);
    }

    /**
     * Update an existing product
     *
     * @param itemId Long
     * @param itemDetails Item
     * @return Item
     */
    public Item updateProduct(Long itemId, Item itemDetails) {
        // Retrieve product from the database
        Item item = itemRepository.findById(itemId)
                .orElseThrow(() -> new RuntimeException("Product not found with id: " + itemId));

        // Update fields
        item.setDescription(itemDetails.getDescription());
        item.setSku(itemDetails.getSku());
        item.setBrand(itemDetails.getBrand());
        item.setSize(itemDetails.getSize());
        item.setUnitOfMeasure(itemDetails.getUnitOfMeasure());
        item.setCost(itemDetails.getCost());
        item.setIsActive(itemDetails.getIsActive());
        item.setIsTaxable(itemDetails.getIsTaxable());
        item.setIsFoodStampable(itemDetails.getIsFoodStampable());
        item.setIsWicEligible(itemDetails.getIsWicEligible());
        item.setIsAgeRestricted(itemDetails.getIsAgeRestricted());
        item.setMinimumAge(itemDetails.getMinimumAge());
        item.setIsReturnable(itemDetails.getIsReturnable());
        item.setIsScaleItem(itemDetails.getIsScaleItem());
        item.setTareWeight(itemDetails.getTareWeight());
        item.setItemImageUrl(itemDetails.getItemImageUrl());
        item.setDepartment(itemDetails.getDepartment());

        // Check for duplicate UPC (excluding current item)
        if (itemDetails.getUpc() != null) {
            Optional<Item> existingItem = itemRepository.findByUpc(itemDetails.getUpc());
            if (existingItem.isPresent() && !existingItem.get().getItemId().equals(itemId)) {
                throw new IllegalArgumentException("UPC already exists");
            }
            item.setUpc(itemDetails.getUpc());
        }

        // Save the updated product to the database
        return itemRepository.save(item);
    }

    /**
     * Get product by ID
     *
     * @param itemId Long
     * @return Optional<Item>
     */
    @Transactional(readOnly = true)
    public Optional<Item> getProductById(Long itemId) {
        // Retrieve product by ID from the database
        return itemRepository.findById(itemId);
    }

    /**
     * Get product by UPC (barcode)
     *
     * @param upc String
     * @return Optional<Item>
     */
    @Transactional(readOnly = true)
    public Optional<Item> getProductByUpc(String upc) {
        // Retrieve product by UPC from the database
        return itemRepository.findByUpc(upc);
    }

    /**
     * Get product by SKU
     *
     * @param sku String
     * @return Optional<Item>
     */
    @Transactional(readOnly = true)
    public Optional<Item> getProductBySku(String sku) {
        // Retrieve product by SKU from the database
        return itemRepository.findBySku(sku);
    }

    /**
     * Search products
     *
     * @param searchTerm String
     * @return List<Item>
     */
    @Transactional(readOnly = true)
    public List<Item> searchProducts(String searchTerm) {
        // Search products from the database
        return itemRepository.searchItems(searchTerm);
    }

    /**
     * Search products with pagination
     *
     * @param searchTerm String
     * @param isActive Boolean
     * @param pageable Pageable
     * @return Page<Item>
     */
    @Transactional(readOnly = true)
    public Page<Item> searchProductsWithPagination(String searchTerm, Boolean isActive, Pageable pageable) {
        // Search products from the database with pagination
        return itemRepository.searchItemsWithPagination(searchTerm, isActive, pageable);
    }

    /**
     * Get all active products (excludes department sale items)
     *
     * @return List<Item>
     */
    @Transactional(readOnly = true)
    public List<Item> getActiveProducts() {
        // Retrieve all active products from the database
        return itemRepository.findByIsActiveTrueAndIsDepartmentSaleFalse();
    }

    /**
     * Get department sale items
     *
     * @return List<Item>
     */
    @Transactional(readOnly = true)
    public List<Item> getDepartmentSaleItems() {
        // Retrieve all department sale items from the database
        return itemRepository.findByIsDepartmentSaleTrue();
    }

    /**
     * Get products by department
     *
     * @param departmentId Long
     * @return List<Item>
     */
    @Transactional(readOnly = true)
    public List<Item> getProductsByDepartment(Long departmentId) {
        // Retrieve products by department ID from the database
        return itemRepository.findByDepartmentDepartmentId(departmentId);
    }

    /**
     * Get scale items
     *
     * @return List<Item>
     */
    @Transactional(readOnly = true)
    public List<Item> getScaleItems() {
        // Retrieve all scale items from the database
        return itemRepository.findByIsScaleItemTrue();
    }

    /**
     * Get WIC eligible items
     *
     * @return List<Item>
     */
    @Transactional(readOnly = true)
    public List<Item> getWicEligibleItems() {
        // Retrieve all WIC eligible items from the database
        return itemRepository.findByIsWicEligibleTrue();
    }

    /**
     * Get age restricted items
     *
     * @return List<Item>
     */
    @Transactional(readOnly = true)
    public List<Item> getAgeRestrictedItems() {
        // Retrieve all age restricted items from the database
        return itemRepository.findByIsAgeRestrictedTrue();
    }

    /**
     * Set product price for a store
     *
     * @param itemId Long
     * @param storeId Long
     * @param price BigDecimal
     * @return ItemPrice
     */
    public ItemPrice setProductPrice(Long itemId, Long storeId, BigDecimal price) {
        // Retrieve item and store from the database
        Item item = itemRepository.findById(itemId)
                .orElseThrow(() -> new RuntimeException("Product not found with id: " + itemId));

        Store store = storeRepository.findById(storeId)
                .orElseThrow(() -> new RuntimeException("Store not found with id: " + storeId));

        // End current price if exists
        Optional<ItemPrice> currentPrice = itemPriceRepository.findCurrentPriceByItemAndStore(item, store);
        if (currentPrice.isPresent()) {
            ItemPrice current = currentPrice.get();
            current.setEndDate(LocalDateTime.now());
            itemPriceRepository.save(current);
        }

        // Create new price
        ItemPrice newPrice = new ItemPrice(item, store, price);
        return itemPriceRepository.save(newPrice);
    }

    /**
     * Get current price for a product in a store
     *
     * @param itemId Long
     * @param storeId Long
     * @return Optional<BigDecimal>
     */
    @Transactional(readOnly = true)
    public Optional<BigDecimal> getCurrentPrice(Long itemId, Long storeId) {
        // Try retrieving item and store from the database
        Item item = itemRepository.findById(itemId).orElse(null);
        Store store = storeRepository.findById(storeId).orElse(null);

        if (item == null || store == null) {
            return Optional.empty();
        }

        // Retrieve the current price of the product for the specified store from the database
        return itemPriceRepository.findCurrentPriceByItemAndStore(item, store)
                .map(ItemPrice::getPrice);
    }

    /**
     * Deactivate product
     *
     * @param itemId Long
     * @return Item
     */
    public Item deactivateProduct(Long itemId) {
        // Retrieve product from the database
        Item item = itemRepository.findById(itemId)
                .orElseThrow(() -> new RuntimeException("Product not found with id: " + itemId));

        // Deactivate the product, set is active false and save to the database
        item.setIsActive(false);
        return itemRepository.save(item);
    }

    /**
     * Activate product
     *
     * @param itemId Long
     * @return Item
     */
    public Item activateProduct(Long itemId) {
        // Retrieve product from the database
        Item item = itemRepository.findById(itemId)
                .orElseThrow(() -> new RuntimeException("Product not found with id: " + itemId));

        // Activate the product, seet is active true, save to the database
        item.setIsActive(true);
        return itemRepository.save(item);
    }

    /**
     * Delete product (soft delete by deactivating)
     *
     * @param itemId Long
     */
    public void deleteProduct(Long itemId) {
        // Soft delete the employee
        deactivateProduct(itemId);
    }

    /**
     * Get product count
     *
     * @return long
     */
    @Transactional(readOnly = true)
    public long getActiveProductCount() {
        // Count all active products from the database
        return itemRepository.countByIsActiveTrue();
    }

    /**
     * Check if product requires age verification
     *
     * @param itemId Long
     * @return boolean
     */
    @Transactional(readOnly = true)
    public boolean requiresAgeVerification(Long itemId) {
        // Get if age verification is required based on the employee id
        return itemRepository.findById(itemId)
                .map(Item::getIsAgeRestricted)
                .orElse(false);
    }

    /**
     * Get minimum age for product
     *
     * @param itemId Long
     * @return int
     */
    @Transactional(readOnly = true)
    public int getMinimumAge(Long itemId) {
        // Get the minimum age for the item based on the item id
        return itemRepository.findById(itemId)
                .map(Item::getMinimumAge)
                .orElse(0);
    }
}