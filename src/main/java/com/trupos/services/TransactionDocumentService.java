package com.trupos.services;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import com.trupos.entities.Customer;
import com.trupos.entities.Employee;
import com.trupos.entities.Lane;
import com.trupos.entities.Store;
import com.trupos.entities.Transaction;
import com.trupos.models.TransactionDocument;
import com.trupos.repositories.CustomerRepository;
import com.trupos.repositories.EmployeeRepository;
import com.trupos.repositories.LaneRepository;
import com.trupos.repositories.StoreRepository;

/**
 * Service for managing transaction documents and converting between
 * relational and document representations
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TransactionDocumentService {

    private final StoreRepository storeRepository;
    private final LaneRepository laneRepository;
    private final EmployeeRepository employeeRepository;
    private final CustomerRepository customerRepository;

    /**
     * Enriches a transaction document with denormalized data for complete storage
     *
     * @param transaction Transaction
     * @return TransactionDocument
     */
    public TransactionDocument enrichTransactionDocument(Transaction transaction) {
        TransactionDocument document = new TransactionDocument(transaction);

        // Add store information
        if (transaction.getStoreId() != null) {
            Store store = storeRepository.findById(transaction.getStoreId()).orElse(null);
            if (store != null) {
                TransactionDocument.StoreInfo storeInfo = new TransactionDocument.StoreInfo();
                storeInfo.setStoreId(store.getStoreId());
                storeInfo.setStoreName(store.getStoreName());
                storeInfo.setStoreNumber(store.getStoreNo());
                storeInfo.setAddress(store.getAddress());
                storeInfo.setCity(store.getCity());
                storeInfo.setState(store.getState());
                storeInfo.setZipCode(store.getZipCode());
                storeInfo.setPhoneNumber(store.getPhoneNumber());
                storeInfo.setTaxRate(store.getTaxRate());
                document.setStoreInfo(storeInfo);
            }
        }

        // Add lane information
        if (transaction.getLaneId() != null) {
            Lane lane = laneRepository.findById(transaction.getLaneId()).orElse(null);
            if (lane != null) {
                TransactionDocument.LaneInfo laneInfo = new TransactionDocument.LaneInfo();
                laneInfo.setLaneId(lane.getLaneId());
                laneInfo.setLaneNumber(lane.getLaneNumber());
                laneInfo.setLaneName(lane.getLaneName());
                document.setLaneInfo(laneInfo);
            }
        }

        // Add employee information
        if (transaction.getEmployeeId() != null) {
            Employee employee = employeeRepository.findById(transaction.getEmployeeId()).orElse(null);
            if (employee != null) {
                TransactionDocument.EmployeeInfo employeeInfo = new TransactionDocument.EmployeeInfo();
                employeeInfo.setEmployeeId(employee.getEmployeeId());
                employeeInfo.setLoginId(employee.getLoginId());
                employeeInfo.setFirstName(employee.getFirstName());
                employeeInfo.setLastName(employee.getLastName());
                document.setEmployeeInfo(employeeInfo);
            }
        }

        // Add customer information
        if (transaction.getCustomerId() != null) {
            Customer customer = customerRepository.findById(transaction.getCustomerId()).orElse(null);
            if (customer != null) {
                TransactionDocument.CustomerInfo customerInfo = new TransactionDocument.CustomerInfo();
                customerInfo.setCustomerId(customer.getCustomerId());
                customerInfo.setFirstName(customer.getFirstName());
                customerInfo.setLastName(customer.getLastName());
                customerInfo.setLoyaltyCardNumber(customer.getLoyaltyCardNumber());
                customerInfo.setTaxExemptId(customer.getTaxExemptId());
                document.setCustomerInfo(customerInfo);
            }
        }

        // Add tax details
        TransactionDocument.TaxDetails taxDetails = new TransactionDocument.TaxDetails();
        if (document.getStoreInfo() != null) {
            taxDetails.setTaxRate(document.getStoreInfo().getTaxRate());
        }

        // Calculate taxable amount
        BigDecimal taxableAmount = transaction.getLineItems().stream()
                .filter(item -> item.getIsTaxable())
                .map(item -> item.getExtendedPrice())
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        taxDetails.setTaxableAmount(taxableAmount);

        // Check if customer is tax exempt
        boolean isTaxExempt = document.getCustomerInfo() != null &&
                document.getCustomerInfo().getTaxExemptId() != null;
        taxDetails.setTaxExempt(isTaxExempt);
        if (isTaxExempt) {
            taxDetails.setTaxExemptReason("Customer Tax Exempt ID: " +
                    document.getCustomerInfo().getTaxExemptId());
        }
        document.setTaxDetails(taxDetails);

        // Add payment summary
        TransactionDocument.PaymentSummary paymentSummary = new TransactionDocument.PaymentSummary();
        BigDecimal totalTendered = transaction.getTenders().stream()
                .map(tender -> tender.getAmount())
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        paymentSummary.setTotalTendered(totalTendered);

        BigDecimal changeDue = totalTendered.subtract(transaction.getTotalAmount());
        if (changeDue.compareTo(BigDecimal.ZERO) > 0) {
            paymentSummary.setChangeDue(changeDue);
        } else {
            paymentSummary.setChangeDue(BigDecimal.ZERO);
        }

        List<String> paymentMethods = transaction.getTenders().stream()
                .map(tender -> tender.getTenderType())
                .distinct()
                .collect(Collectors.toList());
        paymentSummary.setPaymentMethods(paymentMethods);
        document.setPaymentSummary(paymentSummary);

        return document;
    }

    /**
     * Creates a minimal transaction document for new transactions
     *
     * @param storeId Long
     * @param laneId Long
     * @param employeeId Long
     * @param customerId Long
     * @return TransactionDocument
     */
    public TransactionDocument createNewTransactionDocument(Long storeId, Long laneId,
                                                            Long employeeId, Long customerId) {
        TransactionDocument document = new TransactionDocument();
        document.setStoreId(storeId);
        document.setLaneId(laneId);
        document.setEmployeeId(employeeId);
        document.setCustomerId(customerId);
        document.setStatus(Transaction.TransactionStatus.OPEN);
        document.setSubtotal(BigDecimal.ZERO);
        document.setTaxAmount(BigDecimal.ZERO);
        document.setTotalAmount(BigDecimal.ZERO);
        document.setDiscountAmount(BigDecimal.ZERO);
        document.setItemCount(0);
        document.setIsTraining(false);
        document.setIsVoided(false);

        return document;
    }

    /**
     * Updates transaction totals in the document
     *
     * @param document TransactionDocument
     */
    public void updateTransactionTotals(TransactionDocument document) {
        // Calculate subtotal
        BigDecimal subtotal = document.getLineItems().stream()
                .map(item -> item.getExtendedPrice())
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        document.setSubtotal(subtotal);

        // Calculate tax amount
        BigDecimal taxAmount = document.getLineItems().stream()
                .map(item -> item.getTaxAmount())
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        document.setTaxAmount(taxAmount);

        // Calculate total
        BigDecimal total = subtotal.add(taxAmount).subtract(document.getDiscountAmount());
        document.setTotalAmount(total);

        // Update item count
        int itemCount = document.getLineItems().stream()
                .mapToInt(item -> item.getQuantity().intValue())
                .sum();
        document.setItemCount(itemCount);
    }

    /**
     * Validates a transaction document for completeness
     *
     * @param document TransactionDocument
     * @return boolean
     */
    public boolean isTransactionDocumentValid(TransactionDocument document) {
        if (document == null) return false;
        if (document.getStoreId() == null) return false;
        if (document.getLaneId() == null) return false;
        if (document.getEmployeeId() == null) return false;
        if (document.getLineItems() == null || document.getLineItems().isEmpty()) return false;
        if (document.getTotalAmount() == null || document.getTotalAmount().compareTo(BigDecimal.ZERO) < 0) return false;

        return true;
    }

    /**
     * Converts legacy transaction data to document format
     *
     * @param transaction Transaction
     * @return TransactionDocument
     */
    public TransactionDocument convertLegacyToDocument(Transaction transaction) {
        TransactionDocument document = enrichTransactionDocument(transaction);

        // Ensure all required fields are populated
        if (document.getTransactionTimestamp() == null) {
            document.setTransactionTimestamp(transaction.getTransactionTimestamp());
        }

        if (document.getCreatedAt() == null) {
            document.setCreatedAt(transaction.getCreatedAt());
        }

        if (document.getUpdatedAt() == null) {
            document.setUpdatedAt(transaction.getUpdatedAt());
        }

        return document;
    }
}