package com.trupos.services;

import java.util.List;
import java.util.stream.Collectors;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.trupos.dtos.DepartmentDTO;
import com.trupos.entities.Department;
import com.trupos.exceptions.custom.DuplicateNameException;
import com.trupos.exceptions.custom.NotFoundException;
import com.trupos.repositories.DepartmentRepository;
import com.trupos.transformers.DepartmentDTOToEntity;
import com.trupos.transformers.DepartmentToDTO;

/**
 * Service implementation to handle department operations
 */
@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class DepartmentService {

    private final DepartmentToDTO toDTO;
    private final DepartmentDTOToEntity toEntity;
    private final DepartmentRepository departmentRepository;

    /**
     * Creates a new department
     *
     * @param dto DepartmentDTO
     * @return DepartmentDTO
     * @throws DuplicateNameException if department name already exists
     */
    public DepartmentDTO save(DepartmentDTO dto) {

        log.info("save() -> dto: {}", dto);

        // Convert DTO to entity
        Department department = toEntity.apply(dto);

        // Delegate duplicate entry validation logic to helper method
        validateDuplicateName(department, null);

        // Save department to DB
        Department saved = departmentRepository.save(department);

        // Convert back to DTO and return
        return toDTO.apply(saved);
    }

    /**
     * Gets all departments
     *
     * @return List<DepartmentDTO>
     */
    public List<DepartmentDTO> findAll() {

        log.info("findAll()");

        // Find all department(s) and convert to DTO(s) if found
        return departmentRepository.findAll()
                .stream()
                .map(toDTO)
                .collect(Collectors.toList());
    }

    /**
     * Gets active departments only
     *
     * @return List<DepartmentDTO>
     */
    public List<DepartmentDTO> findActive() {

        log.info("findActive()");

        // Find active department(s) and convert to DTO(s) if found
        return departmentRepository.findByIsActiveTrue()
                .stream()
                .map(toDTO)
                .collect(Collectors.toList());
    }

    /**
     * Gets a department by its ID
     *
     * @param id Long
     * @return DepartmentDTO
     * @throws NotFoundException if department does not exist
     */
    public DepartmentDTO findById(Long id) {

        log.info("findById() -> id: {}", id);

        // Find department by given ID, convert to DTO if found, throw NotFoundException if the department does not exist
        return departmentRepository.findById(id)
                .map(toDTO)
                .orElseThrow(() -> new NotFoundException("Department not found with id: " + id));
    }

    /**
     * Gets a department by its name
     *
     * @param name String
     * @return DepartmentDTO
     * @throws NotFoundException if department does not exist
     */
    public DepartmentDTO findByName(String name) {

        log.info("findByName() -> name: {}", name);

        // Find department by given name, convert to DTO if found, throw NotFoundException if the department does not exist
        return departmentRepository.findByDepartmentName(name)
                .map(toDTO)
                .orElseThrow(() -> new NotFoundException("Department not found with name: " + name));
    }

    /**
     * Updates an existing department
     *
     * @param id Long
     * @param dto DepartmentDTO
     * @return DepartmentDTO
     * @throws NotFoundException if department does not exist
     * @throws DuplicateNameException if department name already exists
     */
    public DepartmentDTO update(Long id, DepartmentDTO dto) {

        log.info("update() -> id: {}, dto: {}", id, dto);

        // Ensure the department exists before updating
        departmentRepository.findById(id)
                .orElseThrow(() -> new NotFoundException("Department not found with id: " + id));

        // Assign the ID to the DTO to prevent mismatched updates
        dto.setId(id);

        // Convert DTO to entity
        Department updated = toEntity.apply(dto);

        // Validate duplicates
        validateDuplicateName(updated, id);

        // Save updated department to DB
        Department saved = departmentRepository.save(updated);

        // Convert back to DTO and return
        return toDTO.apply(saved);
    }

    /**
     * Deletes a department
     *
     * @param id Long
     * @throws NotFoundException if department does not exist
     */
    public void delete(Long id) {

        // Find the department by ID, throw NotFoundException if it does not exist
        Department found = departmentRepository.findById(id)
                .orElseThrow(() -> new NotFoundException("Department not found with id: " + id));

        // Delete department if found
        departmentRepository.delete(found);

        log.info("Deleted department with ID: {}", id);
    }

    /**
     * Searches departments by name or description
     *
     * @param searchTerm String
     * @return List<DepartmentDTO>
     */
    @Transactional(readOnly = true)
    public List<DepartmentDTO> search(String searchTerm) {
        log.debug("search() -> term: {}", searchTerm);

        // Search the DB and convert to DTO(s) if found
        List<DepartmentDTO> departments = departmentRepository.searchDepartments(searchTerm)
                .stream()
                .map(toDTO)
                .collect(Collectors.toList());

        log.debug("search() -> Found {} departments", departments.size());

        return departments;
    }

    /**
     * Validates uniqueness of department name
     *
     * @param department Department
     * @param excludeId Long (optional: ID to exclude, e.g. when updating)
     * @throws DuplicateNameException if department name already exists
     */
    private void validateDuplicateName(Department department, Long excludeId) {
        if (department.getName() != null) {
            departmentRepository.findByDepartmentName(department.getName())
                    .filter(existing -> !existing.getId().equals(excludeId))
                    .ifPresent(d -> { throw new DuplicateNameException("Department with name '" + department.getName() + "' already exists"); });
        }
    }
}