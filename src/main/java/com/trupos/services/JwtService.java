package com.trupos.services;

import java.security.Key;
import java.util.Base64;
import java.util.Date;
import jakarta.annotation.PostConstruct;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;

/**
 * Service for generating and validating JWT tokens
 */
@Slf4j
@Service
public class JwtService {

    // Inject secret from application properties
    @Value("${jwt.secret}")
    private String secret;

    private Key key;
    private static final long JWT_EXPIRATION = 24 * 60 * 60 * 1000; // 24h

    @PostConstruct
    public void init() {
        // Secret is base64-encoded, decode it before use
        byte[] keyBytes = Base64.getDecoder().decode(secret);
        this.key = Keys.hmacShaKeyFor(keyBytes);
    }

    /**
     * Generate a JWT token
     *
     * @param username String
     * @return String
     */
    public String generateToken(String username) {
        Date now = new Date();
        Date expiry = new Date(now.getTime() + JWT_EXPIRATION);
        return Jwts.builder()
                .setSubject(username)
                .setIssuedAt(now)
                .setExpiration(expiry)
                .signWith(key, SignatureAlgorithm.HS256)
                .compact();
    }

    /**
     * Extract username from JWT token
     *
     * @param token String
     * @return String
     */
    public String extractUsername(String token) {
        return Jwts.parserBuilder()
                .setSigningKey(key)
                .build()
                .parseClaimsJws(token)
                .getBody()
                .getSubject();
    }

    /**
     * Check if JWT token is valid based on equals username and if the token is not expired
     *
     * @param token String
     * @param username String
     * @return boolean
     */
    public boolean isTokenValid(String token, String username) {
        String extracted = extractUsername(token);
        return extracted.equals(username) && !isTokenExpired(token);
    }

    /**
     * Check if JWT token is expired
     *
     * @param token String
     * @return boolean
     */
    public boolean isTokenExpired(String token) {
        Date expiration = Jwts.parserBuilder()
                .setSigningKey(key)
                .build()
                .parseClaimsJws(token)
                .getBody()
                .getExpiration();
        return expiration.before(new Date());
    }
}