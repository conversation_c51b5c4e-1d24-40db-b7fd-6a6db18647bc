package com.trupos.services;

import java.util.List;
import java.util.Optional;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.trupos.dtos.StoreDTO;
import com.trupos.entities.Store;
import com.trupos.exceptions.custom.DuplicateNameException;
import com.trupos.exceptions.custom.DuplicateStoreNumberException;
import com.trupos.exceptions.custom.NotFoundException;
import com.trupos.repositories.StoreRepository;
import com.trupos.transformers.StoreDTOToEntity;
import com.trupos.transformers.StoreToDTO;

/**
 * Service implementation to handle store operations
 */
@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class StoreService {

    private final StoreToDTO toDTO;
    private final StoreDTOToEntity toEntity;
    private final StoreRepository storeRepository;

    /**
     * Saves a new store
     *
     * @param dto StoreDTO
     * @return StoreDTO
     * @throws DuplicateNameException if store name already exists
     */
    public StoreDTO save(StoreDTO dto) {

        log.info("save() -> dto: {}", dto);

        // Convert DTO to Entity
        Store store = toEntity.apply(dto);

        // Delegate duplicate entry validation logic to helper method
        validateDuplicates(store, null);

        // Save new store to DB
        Store saved = storeRepository.save(store);

        // Convert back to DTO and return
        return toDTO.apply(saved);
    }

    /**
     * Retrieves all stores
     *
     * @return List<StoreDTO>
     */
    @Transactional(readOnly = true)
    public List<StoreDTO> findAll() {

        log.info("findAll() -> retrieving all stores");

        // Retrieve all stores from the database
        List<Store> stores = storeRepository.findAll();

        // Convert to DTOs and return
        return stores.stream()
                .map(toDTO::apply)
                .toList();
    }

    /**
     * Retrieves a store by ID
     *
     * @param id Long
     * @return StoreDTO
     * @throws NotFoundException if store does not exist
     */
    @Transactional(readOnly = true)
    public StoreDTO findById(Long id) {

        log.info("findById() -> id: {}", id);

        // Find store by given ID, throw NotFoundException if the store does not exist
        Store found = storeRepository.findById(id)
                .orElseThrow(() -> new NotFoundException("Store not found with id: " + id));

        // Convert to DTO and return
        return toDTO.apply(found);
    }

    /**
     * Retrieves a store by store number
     *
     * @param storeNo Integer
     * @return StoreDTO
     * @throws NotFoundException if store does not exist
     */
    @Transactional(readOnly = true)
    public StoreDTO findByStoreNumber(Integer storeNo) {

        log.info("findByStoreNumber() -> storeNo: {}", storeNo);

        // Find store by store number, throw NotFoundException if the store does not exist
        Store found = storeRepository.findByStoreNo(storeNo)
                .orElseThrow(() -> new NotFoundException("Store not found with store number: " + storeNo));

        // Convert to DTO and return
        return toDTO.apply(found);
    }

    /**
     * Updates an existing store
     *
     * @param id Long
     * @param dto StoreDTO
     * @return StoreDTO
     * @throws NotFoundException if store does not exist
     * @throws DuplicateNameException if store name already exists
     */
    public StoreDTO update(Long id, StoreDTO dto) {

        log.info("update() -> id: {}, dto: {}", id, dto);

        // Ensure the store exists before updating
        Store found = storeRepository.findById(id)
                .orElseThrow(() -> new NotFoundException("Store not found with id: " + id));

        // Assign the ID to the DTO to prevent mismatched updates
        dto.setId(id);

        // Convert DTO to entity
        Store updated = toEntity.apply(dto);

        // Delegate duplicate entry validation logic to helper method
        validateDuplicates(updated, id);

        // Save updated store to DB
        Store saved = storeRepository.save(updated);

        // Convert back to DTO and return
        return toDTO.apply(saved);
    }

    /**
     * Deletes a store
     *
     * @param id Long
     * @throws NotFoundException if store does not exist
     */
    public void delete(Long id) {

        log.info("delete() -> id: {}", id);

        // Find store by given ID, throw NotFoundException if the store does not exist
        Store found = storeRepository.findById(id)
                .orElseThrow(() -> new NotFoundException("Store not found with id: " + id));

        // Delete found store
        storeRepository.delete(found);
    }

    /**
     * Search stores with pagination
     *
     * @param searchTerm String
     * @param pageable Pageable
     * @return Page<StoreDTO>
     */
    @Transactional(readOnly = true)
    public Page<StoreDTO> searchWithPagination(String searchTerm, Pageable pageable) {

        log.info("searchWithPagination() -> searchTerm: {}, pageable: {}", searchTerm, pageable);

        // If no search term, return all stores with pagination
        Page<Store> stores;
        if (searchTerm == null || searchTerm.trim().isEmpty()) {
            stores = storeRepository.findAll(pageable);
        } else {
            stores = storeRepository.findAll(pageable); // For now, return all stores
        }

        // Convert to DTOs and return
        return stores.map(toDTO::apply);
    }

    /**
     * Validates duplicate entries for store
     *
     * @param store Store entity to validate
     * @param excludeId Long ID to exclude from validation (for updates)
     * @throws DuplicateNameException if store name already exists
     */
    private void validateDuplicates(Store store, Long excludeId) {

        // Check for duplicate store name
        if (store.getName() != null) {
            Optional<Store> existingByName = storeRepository.findByStoreName(store.getName());
            if (existingByName.isPresent() &&
                (excludeId == null || !existingByName.get().getId().equals(excludeId))) {
                throw new DuplicateNameException("Store name already exists: " + store.getName());
            }
        }

        // Check for duplicate store number
        if (store.getStoreNo() != null) {
            Optional<Store> existingByStoreNo = storeRepository.findByStoreNo(store.getStoreNo());
            if (existingByStoreNo.isPresent() &&
                (excludeId == null || !existingByStoreNo.get().getId().equals(excludeId))) {
                throw new DuplicateStoreNumberException("Store number already exists: " + store.getStoreNo());
            }
        }
    }

    /**
     * Retrieves stores by state
     *
     * @param state String
     * @return List<StoreDTO>
     */
    @Transactional(readOnly = true)
    public List<StoreDTO> findByState(String state) {

        log.info("findByState() -> state: {}", state);

        // Retrieve all stores by state from the database
        List<Store> stores = storeRepository.findByState(state);

        // Convert to DTOs and return
        return stores.stream()
                .map(toDTO::apply)
                .toList();
    }

    /**
     * Retrieves stores by city
     *
     * @param city String
     * @return List<StoreDTO>
     */
    @Transactional(readOnly = true)
    public List<StoreDTO> findByCity(String city) {

        log.info("findByCity() -> city: {}", city);

        // Retrieve all stores by city from the database
        List<Store> stores = storeRepository.findByCity(city);

        // Convert to DTOs and return
        return stores.stream()
                .map(toDTO::apply)
                .toList();
    }

    /**
     * Search stores by term
     *
     * @param searchTerm String
     * @return List<StoreDTO>
     */
    @Transactional(readOnly = true)
    public List<StoreDTO> search(String searchTerm) {

        log.info("search() -> searchTerm: {}", searchTerm);

        // If no search term, return all stores
        List<Store> stores;
        if (searchTerm == null || searchTerm.trim().isEmpty()) {
            stores = storeRepository.findAll();
        } else {
            // Retrieve stores from the database based on the provided search term
            stores = storeRepository.searchStores(searchTerm);
        }

        // Convert to DTOs and return
        return stores.stream()
                .map(toDTO::apply)
                .toList();
    }
}