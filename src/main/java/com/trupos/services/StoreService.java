package com.trupos.services;

import java.util.List;
import java.util.Optional;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.trupos.entities.Store;
import com.trupos.repositories.StoreRepository;

/**
 * Service implementation to handle store operations
 */
@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class StoreService {

    private final StoreRepository storeRepository;

    /**
     * Create a new store
     *
     * @param store Store
     * @return Store
     */
    public Store createStore(Store store) {
        // Validate required fields
        if (store.getName() == null || store.getName().trim().isEmpty()) {
            throw new IllegalArgumentException("Store name is required");
        }

        // Check if store number already exists
        if (store.getStoreNo() != null) {
            Optional<Store> existingStore = storeRepository.findByStoreNo(store.getStoreNo());
            if (existingStore.isPresent()) {
                throw new IllegalArgumentException("Store number already exists: " + store.getStoreNo());
            }
        }

        // Save the new store to the database
        return storeRepository.save(store);
    }

    /**
     * Get all stores
     *
     * @return List<Store>
     */
    @Transactional(readOnly = true)
    public List<Store> getAllStores() {
        // Retrieve all stores from the database
        return storeRepository.findAll();
    }

    /**
     * Get store by ID
     *
     * @param id Long
     * @return Optional<Store>
     */
    @Transactional(readOnly = true)
    public Optional<Store> getStoreById(Long id) {
        // Retrieve store by ID from the database
        return storeRepository.findById(id);
    }

    /**
     * Get store by store number
     *
     * @param storeNo Integer
     * @return Optional<Store>
     */
    @Transactional(readOnly = true)
    public Optional<Store> getStoreByNumber(Integer storeNo) {
        // Retrieve store by store number from the database
        return storeRepository.findByStoreNo(storeNo);
    }

    /**
     * Get store by name
     *
     * @param storeName String
     * @return Optional<Store>
     */
    @Transactional(readOnly = true)
    public Optional<Store> getStoreByName(String storeName) {
        // Retrieve store by store name from the database.
        return storeRepository.findByStoreName(storeName);
    }

    /**
     * Update store
     *
     * @param id Long
     * @param storeDetails Store
     * @return Store
     */
    public Store updateStore(Long id, Store storeDetails) {
        // Retrieve store from the database
        Store store = storeRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Store not found with id: " + id));

        // Update fields
        if (storeDetails.getName() != null) {
            store.setName(storeDetails.getName());
        }
        if (storeDetails.getAddress() != null) {
            store.setAddress(storeDetails.getAddress());
        }
        if (storeDetails.getCity() != null) {
            store.setCity(storeDetails.getCity());
        }
        if (storeDetails.getState() != null) {
            store.setState(storeDetails.getState());
        }
        if (storeDetails.getZipCode() != null) {
            store.setZipCode(storeDetails.getZipCode());
        }
        if (storeDetails.getPhoneNumber() != null) {
            store.setPhoneNumber(storeDetails.getPhoneNumber());
        }
        if (storeDetails.getTaxRate() != null) {
            store.setTaxRate(storeDetails.getTaxRate());
        }
        if (storeDetails.getStoreNo() != null) {
            // Check if store number already exists (excluding current store)
            Optional<Store> existingStore = storeRepository.findByStoreNo(storeDetails.getStoreNo());
            if (existingStore.isPresent() && !existingStore.get().getId().equals(id)) {
                throw new IllegalArgumentException("Store number already exists: " + storeDetails.getStoreNo());
            }
            store.setStoreNo(storeDetails.getStoreNo());
        }
        if (storeDetails.getWicStateId() != null) {
            store.setWicStateId(storeDetails.getWicStateId());
        }

        // Save the updated store to the database
        return storeRepository.save(store);
    }

    /**
     * Delete store
     *
     * @param id Long
     */
    public void deleteStore(Long id) {
        // Retrieve store from the database
        Store store = storeRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Store not found with id: " + id));

        // Delete the store
        storeRepository.delete(store);
    }

    /**
     * Search stores with pagination
     *
     * @param searchTerm String
     * @param pageable Pageable
     * @return Pagte<Store>
     */
    @Transactional(readOnly = true)
    public Page<Store> searchStoresWithPagination(String searchTerm, Pageable pageable) {
        // If no search term, return all stores with pagination
        if (searchTerm == null || searchTerm.trim().isEmpty()) {
            return storeRepository.findAll(pageable);
        }
        return storeRepository.findAll(pageable); // For now, return all stores
    }

    /**
     * Get stores by state
     *
     * @param state String
     * @return List<Store>
     */
    @Transactional(readOnly = true)
    public List<Store> getStoresByState(String state) {
        // Retrieve all stores by state from the database
        return storeRepository.findByState(state);
    }

    /**
     * Get stores by city
     *
     * @param city String
     * @return List<Store>
     */
    @Transactional(readOnly = true)
    public List<Store> getStoresByCity(String city) {
        // Retrieve all stores by city from the database
        return storeRepository.findByCity(city);
    }

    /**
     * Search stores by term
     *
     * @param searchTerm String
     * @return List<Store>
     */
    @Transactional(readOnly = true)
    public List<Store> searchStores(String searchTerm) {
        // If no search term, return all stores
        if (searchTerm == null || searchTerm.trim().isEmpty()) {
            return storeRepository.findAll();
        }
        // Retrieve stores from the databas based on the provided search term
        return storeRepository.searchStores(searchTerm);
    }
}