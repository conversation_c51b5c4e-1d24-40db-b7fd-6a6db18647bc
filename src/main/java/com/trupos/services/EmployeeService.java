package com.trupos.services;

import java.util.List;
import java.util.stream.Collectors;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.trupos.config.EmployeeDetailsService;
import com.trupos.dtos.EmployeeDTO;
import com.trupos.entities.Employee;
import com.trupos.entities.Role;
import com.trupos.entities.Store;
import com.trupos.exceptions.custom.DuplicateEmailException;
import com.trupos.exceptions.custom.DuplicateLoginIdException;
import com.trupos.exceptions.custom.NotFoundException;
import com.trupos.models.LoginRequest;
import com.trupos.models.LoginResponse;
import com.trupos.repositories.EmployeeRepository;
import com.trupos.repositories.RoleRepository;
import com.trupos.repositories.StoreRepository;
import com.trupos.transformers.EmployeeDTOToEntity;
import com.trupos.transformers.EmployeeToDTO;

/**
 * Service implementation to handle employee operations
 */
@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class EmployeeService {

    private final EmployeeToDTO toDTO;
    private final EmployeeDTOToEntity toEntity;
    private final JwtService jwtService;
    private final RoleRepository roleRepository;
    private final StoreRepository storeRepository;
    private final AuthenticationManager authManager;
    private final EmployeeRepository employeeRepository;
    private final BCryptPasswordEncoder passwordEncoder;
    private final EmployeeDetailsService employeeDetailsService;

    /**
     * Authenticates an Employee
     *
     * @param request LoginRequest
     * @return LoginResponse
     * @throws NotFoundException if employee not found
     */
    public LoginResponse login(LoginRequest request) {
        log.info("login() -> request: {}", request.getLoginId());

        // Authenticate credentials
        authManager.authenticate(
                new UsernamePasswordAuthenticationToken(request.getLoginId(), request.getPassword())
        );

        // Load user details and entity
        var userDetails = employeeDetailsService.loadUserByUsername(request.getLoginId());
        var employee = employeeRepository.findByLoginId(request.getLoginId())
                .orElseThrow(() -> new UsernameNotFoundException("Employee not found"));

        // Generate JWT token
        String token = jwtService.generateToken(userDetails.getUsername());

        // Build and return response
        return new LoginResponse(token, toDTO.apply(employee));
    }

    /**
     * Save a new employee
     *
     * @param employeeDTO EmployeeDTO
     * @return EmployeeDTO
     * @throws DuplicateLoginIdException if login ID already exists
     * @throws DuplicateEmailException if email already exists
     */
    public EmployeeDTO save(EmployeeDTO employeeDTO) {
        log.info("save() -> employee: {}", employeeDTO.getLoginId());

        // Convert DTO to entity
        Employee employee = toEntity.apply(employeeDTO);

        // Validate duplicates
        validateDuplicates(employee, null);

        // Set the password
        employee.setPassword(passwordEncoder.encode(employee.getPassword()));

        // Save employee to database
        Employee savedEmployee = employeeRepository.save(employee);

        log.info("save() -> savedEmployee: {}", savedEmployee.getLoginId());
        return toDTO.apply(savedEmployee);
    }

    /**
     * Update an employee
     *
     * @param employeeId Long
     * @param employeeDTO EmployeeDTO
     * @return EmployeeDTO
     * @throws NotFoundException if employee not found
     * @throws DuplicateLoginIdException if login ID already exists
     * @throws DuplicateEmailException if email already exists
     */
    public EmployeeDTO update(Long employeeId, EmployeeDTO employeeDTO) {
        log.info("update() -> employeeId: {}, employeeDetails: {}", employeeId, employeeDTO.getLoginId());

        // Retrieve employee from database
        Employee employee = employeeRepository.findById(employeeId)
                .orElseThrow(() -> new NotFoundException("Employee not found with id: " + employeeId));

        // Assign the ID to the DTO to prevent mismatched updates
        employeeDTO.setId(employeeId);

        // Convert DTO to entity
        Employee updatedEmployee = toEntity.apply(employeeDTO);

        // Delegate duplicate entry validation logic to helper method
        validateDuplicates(updatedEmployee, employeeId);

        // Set the password
        updatedEmployee.setPassword(passwordEncoder.encode(updatedEmployee.getPassword()));

        // Save updated employee to database
        Employee savedEmployee = employeeRepository.save(updatedEmployee);

        log.info("update() -> updatedEmployee: {}", savedEmployee.getLoginId());
        return toDTO.apply(savedEmployee);
    }

    /**
     * Authenticate employee by credentials
     *
     * @param loginId String
     * @param password String
     * @return EmployeeDTO
     * @throws NotFoundException if employee not found or employee is inactive
     */
    @Transactional(readOnly = true)
    public EmployeeDTO authenticate(String loginId, String password) {
        log.info("authenticate() -> loginId: {}", loginId);

        // Retrieve employee from database
        Employee employee = employeeRepository.findByLoginId(loginId)
                .orElseThrow(() -> new NotFoundException("Employee not found with login ID: " + loginId));

        // Check if employee is active
        if (!employee.getIsActive()) {
            log.warn("authenticate() -> employee inactive: {}", loginId);
            throw new NotFoundException("Employee is inactive: " + loginId);
        }

        // Check if password matches
        if (!passwordEncoder.matches(password, employee.getPassword())) {
            log.warn("authenticate() -> invalid credentials: {}", loginId);
            throw new NotFoundException("Invalid credentials for employee: " + loginId);
        }

        log.info("authenticate() -> employee authenticated: {}", loginId);
        return toDTO.apply(employee);
    }

    /**
     * Find employee by id
     *
     * @param employeeId Long
     * @return EmployeeDTO
     * @throws NotFoundException if employee not found
     */
    @Transactional(readOnly = true)
    public EmployeeDTO findById(Long employeeId) {
        log.info("findById() -> employeeId: {}", employeeId);

        // Retrieve employee from database
        Employee employee = employeeRepository.findById(employeeId)
                .orElseThrow(() -> new NotFoundException("Employee not found with id: " + employeeId));

        log.info("findById() -> employee found: {}", employee.getLoginId());
        return toDTO.apply(employee);
    }

    /**
     * Find employees by store
     *
     * @param storeId Long
     * @return List<EmployeeDTO>
     */
    @Transactional(readOnly = true)
    public List<EmployeeDTO> findByStore(Long storeId) {
        log.info("findByStore() -> storeId: {}", storeId);

        // Retrieve employees from database
        List<Employee> employees = employeeRepository.findByStoreId(storeId);
        List<EmployeeDTO> employeeDTOs = employees.stream()
                .map(toDTO::apply)
                .collect(Collectors.toList());

        log.info("findByStore() -> employee count: {}", employeeDTOs.size());
        return employeeDTOs;
    }

    /**
     * Find active employees
     *
     * @return List<EmployeeDTO>
     */
    @Transactional(readOnly = true)
    public List<EmployeeDTO> findActive() {
        log.info("findActive()");

        // Retrieve active employees from database
        List<Employee> employees = employeeRepository.findByIsActiveTrue();
        List<EmployeeDTO> employeeDTOs = employees.stream()
                .map(toDTO::apply)
                .collect(Collectors.toList());

        log.info("findActive() -> employee count: {}", employeeDTOs.size());
        return employeeDTOs;
    }

    /**
     * Find active employees by store
     *
     * @param storeId Long
     * @return List<EmployeeDTO>
     * @throws NotFoundException if store not found
     */
    @Transactional(readOnly = true)
    public List<EmployeeDTO> findActiveByStore(Long storeId) {
        log.info("findActiveByStore() -> storeId: {}", storeId);

        // Retrieve store from database
        Store store = storeRepository.findById(storeId)
                .orElseThrow(() -> new NotFoundException("Store not found with id: " + storeId));

        // Retrieve active employees by store from database
        List<Employee> employees = employeeRepository.findByStoreAndIsActiveTrue(store);
        List<EmployeeDTO> employeeDTOs = employees.stream()
                .map(toDTO::apply)
                .collect(Collectors.toList());

        log.info("findActiveByStore() -> employee count: {}", employeeDTOs.size());
        return employeeDTOs;
    }

    /**
     * Get all managers
     *
     * @return List<EmployeeDTO>
     */
    @Transactional(readOnly = true)
    public List<EmployeeDTO> findManagers() {
        log.info("findManagers()");

        // Retrieve managers from database
        List<Employee> managers = employeeRepository.findManagers();
        List<EmployeeDTO> managerDTOs = managers.stream()
                .map(toDTO::apply)
                .collect(Collectors.toList());

        log.info("findManagers() -> manager count: {}", managerDTOs.size());
        return managerDTOs;
    }

    /**
     * Search employees by name
     *
     * @param searchTerm String
     * @return List<EmployeeDTO>
     */
    @Transactional(readOnly = true)
    public List<EmployeeDTO> searchByName(String searchTerm) {
        log.info("searchByName() -> searchTerm: {}", searchTerm);

        // Retrieve employees from database based on search term
        List<Employee> employees = employeeRepository.searchByName(searchTerm);
        List<EmployeeDTO> employeeDTOs = employees.stream()
                .map(toDTO::apply)
                .collect(Collectors.toList());

        log.info("searchByName() -> employee count: {}", employeeDTOs.size());
        return employeeDTOs;
    }

    /**
     * Authorize manager by credentials
     *
     * @param loginId String
     * @param password String
     * @return EmployeeDTO
     * @throws NotFoundException if manager is unauthorized
     */
    @Transactional(readOnly = true)
    public EmployeeDTO authorizeManager(String loginId, String password) {
        log.info("authorizeManager() -> loginId: {}", loginId);

        try {
            // Authenticate employee
            EmployeeDTO employeeDTO = authenticate(loginId, password);
            log.info("authorizeManager() -> manager authorized: {}", loginId);
            return employeeDTO;
        } catch (NotFoundException e) {
            log.warn("authorizeManager() -> manager unauthorized: {}", loginId);
            throw new NotFoundException("Manager unauthorized: " + loginId);
        }
    }

    /**
     * Deactivate an employee
     *
     * @param employeeId Long
     * @return EmployeeDTO
     * @throws NotFoundException if employee not found
     */
    public EmployeeDTO deactivate(Long employeeId) {
        log.info("deactivate() -> employeeId: {}", employeeId);

        // Delegate action to changeActiveStatus and return employee
        return changeActiveStatus(employeeId, false);
    }

    /**
     * Activate an employee
     *
     * @param employeeId Long
     * @return EmployeeDTO
     * @throws NotFoundException if employee not found
     */
    public EmployeeDTO activate(Long employeeId) {
        log.info("activate() -> employeeId: {}", employeeId);

        // Delegate action to changeActiveStatus and return employee
        return changeActiveStatus(employeeId, true);
    }

    /**
     * Assign role to employee
     *
     * @param employeeId Long
     * @param roleId Long
     * @return EmployeeDTO
     * @throws NotFoundException if employee or role not found
     */
    public EmployeeDTO assignRole(Long employeeId, Long roleId) {
        log.info("assignRole() -> employeeId: {}, roleId: {}", employeeId, roleId);

        // Retrieve employee from database
        Employee employee = employeeRepository.findById(employeeId)
                .orElseThrow(() -> new NotFoundException("Employee not found with id: " + employeeId));

        // Retrieve role from database
        Role role = roleRepository.findById(roleId)
                .orElseThrow(() -> new NotFoundException("Role not found with id: " + roleId));

        // Assign role to employee
        employee.setRole(role);
        Employee assignedRoleEmployee = employeeRepository.save(employee);

        log.info("assignRole() -> employee role assigned: {}", assignedRoleEmployee.getLoginId());
        return toDTO.apply(assignedRoleEmployee);
    }

    /**
     * Assign store to employee
     *
     * @param employeeId Long
     * @param storeId Long
     * @return EmployeeDTO
     * @throws NotFoundException if employee or store not found
     */
    public EmployeeDTO assignStore(Long employeeId, Long storeId) {
        log.info("assignStore() -> employeeId: {}, storeId: {}", employeeId, storeId);

        // Retrieve employee from database
        Employee employee = employeeRepository.findById(employeeId)
                .orElseThrow(() -> new NotFoundException("Employee not found with id: " + employeeId));

        // Retrieve store from database
        Store store = storeRepository.findById(storeId)
                .orElseThrow(() -> new NotFoundException("Store not found with id: " + storeId));

        employee.setStoreId(storeId);
        Employee assignedStoreEmployee = employeeRepository.save(employee);

        log.info("assignStore() -> employee store assigned: {}", assignedStoreEmployee.getLoginId());
        return toDTO.apply(assignedStoreEmployee);
    }

    /**
     * Changes the active status of a employee
     *
     * @param id Long
     * @param isActive boolean
     * @return EmployeeDTO
     * @throws NotFoundException if employee not found
     */
    private EmployeeDTO changeActiveStatus(Long id, boolean isActive) {

        // Find employee by given ID, throw NotFoundException if the employee does not exist.
        Employee employee = employeeRepository.findById(id)
                .orElseThrow(() -> new NotFoundException("Employee not found with id: " + id));

        // Set isActive to passed value
        employee.setIsActive(isActive);

        // Save updated employee to DB
        Employee saved = employeeRepository.save(employee);

        // Convert to DTO and return
        return toDTO.apply(saved);
    }

    /**
     * Validates uniqueness of login id and email for a customer
     *
     * @param employee Employee
     * @param excludeId Long
     * @throws DuplicateLoginIdException if login id already exists
     * @throws DuplicateEmailException if email already exists
     */
    private void validateDuplicates(Employee employee, Long excludeId) {

        // Check for duplicate login id if provided
        // Exclude employee from being updated (if applicable)
        // Throw DuplicateLoginIdException if duplicate found
        if (employee.getLoginId() != null) {
            employeeRepository.findByLoginId(employee.getLoginId())
                    .filter(existing -> !existing.getId().equals(excludeId))
                    .ifPresent(c -> {
                        throw new DuplicateLoginIdException("Login ID already exists");
                    });
        }

        // Check for duplicate email if provided
        // Exclude employee from being updated (if applicable)
        // Throw DuplicateEmailException if duplicate found
        if (employee.getEmail() != null) {
            employeeRepository.findByEmail(employee.getEmail())
                    .filter(existing -> !existing.getId().equals(excludeId))
                    .ifPresent(c -> {
                        throw new DuplicateEmailException("Email already exists");
                    });
        }
    }
}