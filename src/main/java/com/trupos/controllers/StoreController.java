package com.trupos.controllers;

import java.util.List;
import jakarta.validation.Valid;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.trupos.dtos.StoreDTO;
import com.trupos.services.StoreService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

/**
 * Controller implementation to handle store requests
 */
@Slf4j
@RestController
@RequestMapping("/stores")
@RequiredArgsConstructor
public class StoreController {

    private final StoreService storeService;

    /**
     * Process a store creation request
     *
     * @param payload StoreDTO
     * @return ResponseEntity<StoreDTO>
     */
    @Operation(summary = "Create a new store")
    @PostMapping
    public ResponseEntity<StoreDTO> create(@Valid @RequestBody StoreDTO payload) {

        log.info("create() -> payload: {}", payload);

        // Delegate store creation to service
        StoreDTO dto = storeService.save(payload);

        // Return CREATED with Store data
        log.info("create() -> dto: {}", dto);
        return ResponseEntity.status(HttpStatus.CREATED).body(dto);
    }

    /**
     * Fetch all stores
     *
     * @return ResponseEntity<List<StoreDTO>>
     */
    @Operation(summary = "Get all stores")
    @GetMapping
    public ResponseEntity<List<StoreDTO>> getAll() {

        log.info("getAll() -> fetching all stores");

        // Delegate fetching all stores to service
        List<StoreDTO> dtos = storeService.findAll();

        // Return OK with Store data
        log.info("getAll() -> dtos: {}", dtos);
        return ResponseEntity.status(HttpStatus.OK).body(dtos);
    }

    /**
     * Fetch a store by ID
     *
     * @param id Long
     * @return ResponseEntity<StoreDTO>
     */
    @Operation(summary = "Get store by ID")
    @GetMapping("/{id}")
    public ResponseEntity<StoreDTO> getById(@PathVariable Long id) {

        log.info("getById() -> id: {}", id);

        // Delegate fetching store to service
        StoreDTO dto = storeService.findById(id);

        // Return OK with Store data
        log.info("getById() -> dto: {}", dto);
        return ResponseEntity.status(HttpStatus.OK).body(dto);
    }

    /**
     * Fetch a store by store number
     *
     * @param storeNo Integer
     * @return ResponseEntity<StoreDTO>
     */
    @Operation(summary = "Get store by store number")
    @GetMapping("/number/{storeNo}")
    public ResponseEntity<StoreDTO> getByStoreNumber(@PathVariable Integer storeNo) {

        log.info("getByStoreNumber() -> storeNo: {}", storeNo);

        // Delegate fetching store to service
        StoreDTO dto = storeService.findByStoreNumber(storeNo);

        // Return OK with Store data
        log.info("getByStoreNumber() -> dto: {}", dto);
        return ResponseEntity.status(HttpStatus.OK).body(dto);
    }

    /**
     * Update a store
     *
     * @param id Long
     * @param payload StoreDTO
     * @return ResponseEntity<StoreDTO>
     */
    @Operation(summary = "Update store")
    @PutMapping("/{id}")
    public ResponseEntity<StoreDTO> update(@PathVariable Long id, @Valid @RequestBody StoreDTO payload) {

        log.info("update() -> id: {}, payload: {}", id, payload);

        // Delegate updating store to service
        StoreDTO dto = storeService.update(id, payload);

        // Return OK with Store data
        log.info("update() -> dto: {}", dto);
        return ResponseEntity.status(HttpStatus.OK).body(dto);
    }

    /**
     * Delete a store
     *
     * @param id Long
     * @return ResponseEntity<Void>
     */
    @Operation(summary = "Delete store")
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> delete(@PathVariable Long id) {

        log.info("delete() -> id: {}", id);

        // Delegate deleting store to service
        storeService.delete(id);

        // Return NO_CONTENT
        log.info("delete() -> store deleted successfully");
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    /**
     * Search stores with pagination
     *
     * @param q String
     * @param page int
     * @param size int
     * @param sortBy String
     * @param sortDir String
     * @return ResponseEntity<Page<StoreDTO>>
     */
    @Operation(summary = "Search stores")
    @GetMapping("/search")
    public ResponseEntity<Page<StoreDTO>> search(
            @Parameter(description = "Search term") @RequestParam(required = false) String q,
            @Parameter(description = "Page number") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "Sort by field") @RequestParam(defaultValue = "name") String sortBy,
            @Parameter(description = "Sort direction") @RequestParam(defaultValue = "asc") String sortDir) {

        log.info("search() -> q: {}, page: {}, size: {}, sortBy: {}, sortDir: {}", q, page, size, sortBy, sortDir);

        Sort sort = sortDir.equalsIgnoreCase("desc") ?
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);

        // Delegate searching stores to service
        Page<StoreDTO> stores = storeService.searchWithPagination(q, pageable);

        // Return OK with Store data
        log.info("search() -> stores: {}", stores);
        return ResponseEntity.status(HttpStatus.OK).body(stores);
    }

    /**
     * Fetch stores by state
     *
     * @param state String
     * @return ResponseEntity<List<StoreDTO>>
     */
    @Operation(summary = "Get stores by state")
    @GetMapping("/state/{state}")
    public ResponseEntity<List<StoreDTO>> getByState(@PathVariable String state) {

        log.info("getByState() -> state: {}", state);

        // Delegate fetching stores to service
        List<StoreDTO> dtos = storeService.findByState(state);

        // Return OK with Store data
        log.info("getByState() -> dtos: {}", dtos);
        return ResponseEntity.status(HttpStatus.OK).body(dtos);
    }

    /**
     * Fetch stores by city
     *
     * @param city String
     * @return ResponseEntity<List<StoreDTO>>
     */
    @Operation(summary = "Get stores by city")
    @GetMapping("/city/{city}")
    public ResponseEntity<List<StoreDTO>> getByCity(@PathVariable String city) {

        log.info("getByCity() -> city: {}", city);

        // Delegate fetching stores to service
        List<StoreDTO> dtos = storeService.findByCity(city);

        // Return OK with Store data
        log.info("getByCity() -> dtos: {}", dtos);
        return ResponseEntity.status(HttpStatus.OK).body(dtos);
    }
}