package com.trupos.controllers;

import java.util.List;
import java.util.Optional;
import jakarta.validation.Valid;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.trupos.entities.Store;
import com.trupos.services.StoreService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

/**
 * Controller implementation to handle store requests
 */
@Slf4j
@RestController
@RequestMapping("/stores")
@RequiredArgsConstructor
public class StoreController {

    private final StoreService storeService;

    /**
     * Create a new store
     *
     * @param store Store
     * @return ResponseEntity<Store>
     */
    @Operation(summary = "Create a new store")
    @PostMapping
    public ResponseEntity<Store> createStore(@Valid @RequestBody Store store) {
        try {
            // Delegate creation of store to service
            Store createdStore = storeService.createStore(store);
            return new ResponseEntity<>(createdStore, HttpStatus.CREATED);
        } catch (IllegalArgumentException e) {
            return new ResponseEntity<>(null, HttpStatus.BAD_REQUEST);
        }
    }

    /**
     * Get all stores
     *
     * @return ResponseEntity<List<Store>>
     */
    @Operation(summary = "Get all stores")
    @GetMapping
    public ResponseEntity<List<Store>> getAllStores() {
        // Delegate fetching all stores to service
        List<Store> stores = storeService.getAllStores();
        return ResponseEntity.ok(stores);
    }

    /**
     * Get store by ID
     *
     * @param id Long
     * @return ResponseEntity<Store>
     */
    @Operation(summary = "Get store by ID")
    @GetMapping("/{id}")
    public ResponseEntity<Store> getStoreById(
            @Parameter(description = "Store ID") @PathVariable Long id) {
        // Delegate fetching store to service
        Optional<Store> store = storeService.getStoreById(id);
        return store.map(s -> ResponseEntity.ok(s))
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Get store by store number
     *
     * @param storeNo Integer
     * @return ResponseEntity<Store>
     */
    @Operation(summary = "Get store by store number")
    @GetMapping("/number/{storeNo}")
    public ResponseEntity<Store> getStoreByNumber(
            @Parameter(description = "Store number") @PathVariable Integer storeNo) {
        // Delegate fetching store to service
        Optional<Store> store = storeService.getStoreByNumber(storeNo);
        return store.map(s -> ResponseEntity.ok(s))
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Update store
     *
     * @param id Long
     * @param store Store
     * @return ResponseEntity<Store>
     */
    @Operation(summary = "Update store")
    @PutMapping("/{id}")
    public ResponseEntity<Store> updateStore(
            @Parameter(description = "Store ID") @PathVariable Long id,
            @Valid @RequestBody Store store) {
        try {
            // Delegate updating store to service
            Store updatedStore = storeService.updateStore(id, store);
            return ResponseEntity.ok(updatedStore);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Delete store
     *
     * @param id Long
     * @return ResponseEntity<Void>
     */
    @Operation(summary = "Delete store")
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteStore(
            @Parameter(description = "Store ID") @PathVariable Long id) {
        try {
            // Delegate deleting store to service
            storeService.deleteStore(id);
            return ResponseEntity.noContent().build();
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Search stores
     *
     * @param q String
     * @param page int
     * @param size int
     * @param sortBy String
     * @param sortDir String
     * @return ResponseEntity<Page<Store>>
     */
    @Operation(summary = "Search stores")
    @GetMapping("/search")
    public ResponseEntity<Page<Store>> searchStores(
            @Parameter(description = "Search term") @RequestParam(required = false) String q,
            @Parameter(description = "Page number") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "Sort by field") @RequestParam(defaultValue = "storeName") String sortBy,
            @Parameter(description = "Sort direction") @RequestParam(defaultValue = "asc") String sortDir) {

        Sort sort = sortDir.equalsIgnoreCase("desc") ?
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);

        // Delegate searching stores to service
        Page<Store> stores = storeService.searchStoresWithPagination(q, pageable);
        return ResponseEntity.ok(stores);
    }

    /**
     * Get stores by state
     *
     * @param state String
     * @return ResponseEntity<List<Store>>
     */
    @Operation(summary = "Get stores by state")
    @GetMapping("/state/{state}")
    public ResponseEntity<List<Store>> getStoresByState(
            @Parameter(description = "State code") @PathVariable String state) {
        // Delegate fetching stores to service
        List<Store> stores = storeService.getStoresByState(state);
        return ResponseEntity.ok(stores);
    }

    /**
     * Get stores by city
     *
     * @param city String
     * @return ResponseEntity<List<Store>>
     */
    @Operation(summary = "Get stores by city")
    @GetMapping("/city/{city}")
    public ResponseEntity<List<Store>> getStoresByCity(
            @Parameter(description = "City name") @PathVariable String city) {
        // Delegate fetching stores to service
        List<Store> stores = storeService.getStoresByCity(city);
        return ResponseEntity.ok(stores);
    }
}