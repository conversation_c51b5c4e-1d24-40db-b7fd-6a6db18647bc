package com.trupos.controllers;

import jakarta.validation.Valid;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.trupos.models.LoginRequest;
import com.trupos.models.LoginResponse;
import com.trupos.services.EmployeeService;

/**
 * Controller implementation to handle login requests
 */
@Slf4j
@RestController
@RequestMapping("/v1")
@RequiredArgsConstructor
public class LoginController {

    private final EmployeeService employeeService;

    /**
     * Process a login request
     *
     * @param request LoginRequest
     * @return ResponseEntity<LoginResponse>
     */
    @PostMapping("/login")
    public ResponseEntity<LoginResponse> login(@RequestBody @Valid LoginRequest request) {

        log.info("login() -> request: {}", request.getLoginId());

        // Delegate authentication to service
        LoginResponse response = employeeService.login(request);

        // Return OK with Employee data and token
        return ResponseEntity.status(HttpStatus.OK).body(response);
    }
}