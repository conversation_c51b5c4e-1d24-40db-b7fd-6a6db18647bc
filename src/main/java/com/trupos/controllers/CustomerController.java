package com.trupos.controllers;

import java.util.List;
import jakarta.validation.Valid;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.trupos.dtos.CustomerDTO;
import com.trupos.models.SearchDetails;
import com.trupos.services.CustomerService;

/**
 * Controller implementation to handle customer requests
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/customers")
public class CustomerController {

    private final CustomerService customerService;

    /**
     * Process a customer creation request
     *
     * @param payload CustomerDTO
     * @return ResponseEntity<CustomerDTO>
     */
    @PostMapping
    public ResponseEntity<CustomerDTO> create(@Valid @RequestBody CustomerDTO payload) {

        log.info("create() -> payload: {}", payload);

        // Delegate customer creation to service
        CustomerDTO dto = customerService.save(payload);

        // Return CREATED with Customer data
        log.info("create() -> dto: {}", dto);
        return ResponseEntity.status(HttpStatus.CREATED).body(dto);
    }

    /**
     * Process a get customer by ID request
     *
     * @param id Long
     * @return ResponseEntity<CustomerDTO>
     */
    @GetMapping("/{id}")
    public ResponseEntity<CustomerDTO> getById(@PathVariable Long id) {

        log.info("getById() -> id: {}", id);

        // Delegate fetching customer to service
        CustomerDTO dto = customerService.findById(id);

        // Return OK with Customer data
        log.info("getById() -> dto: {}", dto);
        return ResponseEntity.status(HttpStatus.OK).body(dto);
    }

    /**
     * Process an update customer request
     *
     * @param id Long
     * @param payload CustomerDTO
     * @return ResponseEntity<CustomerDTO>
     */
    @PutMapping("/{id}")
    public ResponseEntity<CustomerDTO> update(@PathVariable Long id, @Valid @RequestBody CustomerDTO payload) {

        log.info("update() -> id: {}, payload: {}", id, payload);

        // Delegate updating customer to service
        CustomerDTO dto = customerService.update(id, payload);

        // Return OK with Customer data
        log.info("update() -> dto: {}", dto);
        return ResponseEntity.status(HttpStatus.OK).body(dto);
    }

    /**
     * Process a deactivate customer request
     *
     * @param id Long
     * @return ResponseEntity<CustomerDTO>
     */
    @PatchMapping("/{id}/deactivate")
    public ResponseEntity<CustomerDTO> deactivate(@PathVariable Long id) {

        log.info("deactivate() -> id: {}", id);

        // Delegate deactivating customer to service
        CustomerDTO dto = customerService.deactivate(id);

        // Return OK with Customer data
        log.info("deactivate() -> dto: {}", dto);
        return ResponseEntity.status(HttpStatus.OK).body(dto);
    }

    /**
     * Process an activate customer request
     *
     * @param id Long
     * @return ResponseEntity<CustomerDTO>
     */
    @PatchMapping("/{id}/activate")
    public ResponseEntity<CustomerDTO> activate(@PathVariable Long id) {

        log.info("activate() -> id: {}", id);

        // Delegate activating customer to service
        CustomerDTO dto = customerService.activate(id);

        // Return OK with Customer data
        log.info("activate() -> dto: {}", dto);
        return ResponseEntity.status(HttpStatus.OK).body(dto);
    }

    /**
     * Process a delete customer request
     *
     * @param id Long
     * @return ResponseEntity<Void>
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> delete(@PathVariable Long id) {

        log.info("delete() -> id: {}", id);

        // Delegate deleting customer to service
        customerService.delete(id);

        // Return NO_CONTENT
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    /**
     * Process a get customer by loyalty card request
     *
     * @param loyaltyCardNumber String
     * @return ResponseEntity<CustomerDTO>
     */
    @GetMapping("/loyalty/{loyaltyCardNumber}")
    public ResponseEntity<CustomerDTO> getByLoyaltyCard(@PathVariable String loyaltyCardNumber) {

        log.info("getByLoyaltyCard() -> loyaltyCardNumber: {}", loyaltyCardNumber);

        // Delegate fetching customer to service
        CustomerDTO dto = customerService.findByLoyaltyCard(loyaltyCardNumber);

        // Return OK with Customer data
        log.info("getByLoyaltyCard() -> dto: {}", dto);
        return ResponseEntity.status(HttpStatus.OK).body(dto);
    }

    /**
     * Process a get customer by email request
     *
     * @param email String
     * @return ResponseEntity<CustomerDTO>
     */
    @GetMapping("/email/{email}")
    public ResponseEntity<CustomerDTO> getByEmail(@PathVariable String email) {

        log.info("getByEmail() -> email: {}", email);

        // Delegate fetching customer to service
        CustomerDTO dto = customerService.findByEmail(email);

        // Return OK with Customer data
        log.info("getByEmail() -> dto: {}", dto);
        return ResponseEntity.status(HttpStatus.OK).body(dto);
    }

    /**
     * Process a get customer(s) by phone number request
     *
     * @param phoneNumber String
     * @return ResponseEntity<List<CustomerDTO>>
     */
    @GetMapping("/phone/{phoneNumber}")
    public ResponseEntity<List<CustomerDTO>> getByPhone(@PathVariable String phoneNumber) {

        log.info("getByPhone() -> phoneNumber: {}", phoneNumber);

        // Delegate fetching customer(s) to service
        List<CustomerDTO> DTOs = customerService.findByPhone(phoneNumber);

        // Return OK with Customer data
        log.info("getByPhone() -> DTOs: {}", DTOs.size());
        return ResponseEntity.status(HttpStatus.OK).body(DTOs);
    }

    /**
     * Process a get all active customers request
     *
     * @return ResponseEntity<List<CustomerDTO>>
     */
    @GetMapping("/active")
    public ResponseEntity<List<CustomerDTO>> getActive() {

        log.info("getActive()");

        // Delegate fetching customer(s) to service
        List<CustomerDTO> DTOs = customerService.findActive();

        // Return OK with Customer data
        log.info("getActive() -> DTOs: {}", DTOs.size());
        return ResponseEntity.status(HttpStatus.OK).body(DTOs);
    }

    /**
     * Process a search customers request
     *
     * @param details SearchDetails
     * @return ResponseEntity<Page<Customer>>
     */
    @GetMapping("/search")
    public ResponseEntity<Page<CustomerDTO>> search(@Valid @ModelAttribute SearchDetails details) {

        log.info("search() -> details: {}", details);

        // Build a Sort object based on a direction
        Sort sort = details.getSortDir().equalsIgnoreCase("desc") ?
                Sort.by(details.getSortBy()).descending() : Sort.by(details.getSortBy()).ascending();

        // Build a Pageable object with page number, size, and sorting direction
        Pageable pageable = PageRequest.of(details.getPage(), details.getSize(), sort);

        // Delegate search to service
        Page<CustomerDTO> DTOs = customerService.search(details.getQ(), pageable);

        // Return OK with Customer data
        log.info("search() -> DTOs: {}", DTOs.getTotalElements());
        return ResponseEntity.status(HttpStatus.OK).body(DTOs);
    }
}