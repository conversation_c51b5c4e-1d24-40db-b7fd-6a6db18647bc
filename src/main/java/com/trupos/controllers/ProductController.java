package com.trupos.controllers;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import jakarta.validation.Valid;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.trupos.entities.Item;
import com.trupos.entities.ItemPrice;
import com.trupos.services.ProductService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

/**
 * Controller implementation to handle product requests
 */
@Slf4j
@RestController
@RequestMapping("/products")
@RequiredArgsConstructor
public class ProductController {

    private final ProductService productService;

    /**
     * Create a new product
     *
     * @param item Item
     * @return ResponseEntity<Item>
     */
    @Operation(summary = "Create a new product")
    @PostMapping
    public ResponseEntity<Item> createProduct(@Valid @RequestBody Item item) {
        try {
            // Delegate creation of product to service
            Item createdItem = productService.createProduct(item);
            return new ResponseEntity<>(createdItem, HttpStatus.CREATED);
        } catch (IllegalArgumentException e) {
            return new ResponseEntity<>(null, HttpStatus.BAD_REQUEST);
        }
    }

    /**
     * Get product by ID
     *
     * @param id Long
     * @return ResponseEntity<Item>
     */
    @Operation(summary = "Get product by ID")
    @GetMapping("/{id}")
    public ResponseEntity<Item> getProductById(
            @Parameter(description = "Product ID") @PathVariable Long id) {
        // Delegate fetching product to service
        Optional<Item> item = productService.getProductById(id);
        return item.map(i -> ResponseEntity.ok(i))
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Get product by UPC/barcode
     *
     * @param upc String
     * @return ResponseEntity<Item>
     */
    @Operation(summary = "Get product by UPC/barcode")
    @GetMapping("/upc/{upc}")
    public ResponseEntity<Item> getProductByUpc(
            @Parameter(description = "UPC/barcode") @PathVariable String upc) {
        // Delegate fetching product to service
        Optional<Item> item = productService.getProductByUpc(upc);
        return item.map(i -> ResponseEntity.ok(i))
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Get product by SKU
     *
     * @param sku String
     * @return ResponseEntity<Item>
     */
    @Operation(summary = "Get product by SKU")
    @GetMapping("/sku/{sku}")
    public ResponseEntity<Item> getProductBySku(
            @Parameter(description = "SKU") @PathVariable String sku) {
        // Delegate fetching product to service
        Optional<Item> item = productService.getProductBySku(sku);
        return item.map(i -> ResponseEntity.ok(i))
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Update product
     *
     * @param id Long
     * @param itemDetails Item
     * @return ResponseEntity<Item>
     */
    @Operation(summary = "Update product")
    @PutMapping("/{id}")
    public ResponseEntity<Item> updateProduct(
            @Parameter(description = "Product ID") @PathVariable Long id,
            @Valid @RequestBody Item itemDetails) {
        try {
            // Delegate updating product to service
            Item updatedItem = productService.updateProduct(id, itemDetails);
            return ResponseEntity.ok(updatedItem);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Delete product
     *
     * @param id Long
     * @return ResponseEntity<Void>
     */
    @Operation(summary = "Delete product")
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteProduct(
            @Parameter(description = "Product ID") @PathVariable Long id) {
        try {
            // Delegate deleting product to service
            productService.deleteProduct(id);
            return ResponseEntity.noContent().build();
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Search products
     *
     * @param q String
     * @param active Boolean
     * @param page int
     * @param size int
     * @param sortBy String
     * @param sortDir String
     * @return ResponseEntity<Page<Item>>
     */
    @Operation(summary = "Search products")
    @GetMapping("/search")
    public ResponseEntity<Page<Item>> searchProducts(
            @Parameter(description = "Search term") @RequestParam(required = false) String q,
            @Parameter(description = "Filter by active status") @RequestParam(required = false) Boolean active,
            @Parameter(description = "Page number") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "Sort by field") @RequestParam(defaultValue = "description") String sortBy,
            @Parameter(description = "Sort direction") @RequestParam(defaultValue = "asc") String sortDir) {

        Sort sort = sortDir.equalsIgnoreCase("desc") ?
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);

        // Delegate searching products to service
        Page<Item> items = productService.searchProductsWithPagination(q, active, pageable);
        return ResponseEntity.ok(items);
    }

    /**
     * Get all active products (excludes department sale items)
     *
     * @return ResponseEntity<List<Item>>
     */
    @Operation(summary = "Get all active products (excludes department sale items)")
    @GetMapping("/active")
    public ResponseEntity<List<Item>> getActiveProducts() {
        // Delegate fetching active products to service
        List<Item> items = productService.getActiveProducts();
        return ResponseEntity.ok(items);
    }

    /**
     * Get department sale items
     *
     * @return ResponseEntity<List<Item>>
     */
    @Operation(summary = "Get department sale items")
    @GetMapping("/department-sale")
    public ResponseEntity<List<Item>> getDepartmentSaleItems() {
        // Delegate fetching department sale items to service
        List<Item> items = productService.getDepartmentSaleItems();
        return ResponseEntity.ok(items);
    }

    /**
     * Get products by department
     *
     * @param departmentId Long
     * @return ResponseEntity<List<Item>>
     */
    @Operation(summary = "Get products by department")
    @GetMapping("/department/{departmentId}")
    public ResponseEntity<List<Item>> getProductsByDepartment(
            @Parameter(description = "Department ID") @PathVariable Long departmentId) {
        // Delegate fetching products by department to service
        List<Item> items = productService.getProductsByDepartment(departmentId);
        return ResponseEntity.ok(items);
    }

    /**
     * Set product price
     *
     * @param id Long
     * @param storeId Long
     * @param price BigDecimal
     * @return ResponseEntity<ItemPrice>
     */
    @Operation(summary = "Set product price")
    @PostMapping("/{id}/price")
    public ResponseEntity<ItemPrice> setProductPrice(
            @Parameter(description = "Product ID") @PathVariable Long id,
            @Parameter(description = "Store ID") @RequestParam Long storeId,
            @Parameter(description = "Price") @RequestParam BigDecimal price) {
        try {
            // Delegate setting product price to service
            ItemPrice itemPrice = productService.setProductPrice(id, storeId, price);
            return ResponseEntity.ok(itemPrice);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Get current price for product
     *
     * @param id Long
     * @param storeId Long
     * @return ResponseEntity<BigDecimal>
     */
    @Operation(summary = "Get current price for product")
    @GetMapping("/{id}/price")
    public ResponseEntity<BigDecimal> getCurrentPrice(
            @Parameter(description = "Product ID") @PathVariable Long id,
            @Parameter(description = "Store ID") @RequestParam Long storeId) {
        // Delegate fetching current price to service
        Optional<BigDecimal> price = productService.getCurrentPrice(id, storeId);
        return price.map(p -> ResponseEntity.ok(p))
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Activate product
     *
     * @param id Long
     * @return ResponseEntity<Item>
     */
    @Operation(summary = "Activate product")
    @PutMapping("/{id}/activate")
    public ResponseEntity<Item> activateProduct(
            @Parameter(description = "Product ID") @PathVariable Long id) {
        try {
            // Delegate activating product to service
            Item item = productService.activateProduct(id);
            return ResponseEntity.ok(item);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Deactivate product
     *
     * @param id Long
     * @return ResponseEntity<Item>
     */
    @Operation(summary = "Deactivate product")
    @PutMapping("/{id}/deactivate")
    public ResponseEntity<Item> deactivateProduct(
            @Parameter(description = "Product ID") @PathVariable Long id) {
        try {
            // Delegate deactivating product to service
            Item item = productService.deactivateProduct(id);
            return ResponseEntity.ok(item);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Check if product requires age verification
     *
     * @param id Long
     * @return ResponseEntity<Boolean>
     */
    @Operation(summary = "Check if product requires age verification")
    @GetMapping("/{id}/age-verification")
    public ResponseEntity<Boolean> requiresAgeVerification(
            @Parameter(description = "Product ID") @PathVariable Long id) {
        // Delegate checking age verification requirement to service
        boolean requiresAge = productService.requiresAgeVerification(id);
        return ResponseEntity.ok(requiresAge);
    }

    /**
     * Get minimum age for product
     *
     * @param id Long
     * @return ResponseEntity<Integer>
     */
    @Operation(summary = "Get minimum age for product")
    @GetMapping("/{id}/minimum-age")
    public ResponseEntity<Integer> getMinimumAge(
            @Parameter(description = "Product ID") @PathVariable Long id) {
        // Delegate getting minimum age to service
        int minimumAge = productService.getMinimumAge(id);
        return ResponseEntity.ok(minimumAge);
    }

    /**
     * Get product count
     *
     * @return ResponseEntity<Long>
     */
    @Operation(summary = "Get product count")
    @GetMapping("/count")
    public ResponseEntity<Long> getProductCount() {
        // Delegate getting product count to service
        long count = productService.getActiveProductCount();
        return ResponseEntity.ok(count);
    }
}