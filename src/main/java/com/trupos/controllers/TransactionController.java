package com.trupos.controllers;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.trupos.dtos.TransactionDTO;
import com.trupos.entities.Transaction;
import com.trupos.models.TransactionLineItemData;
import com.trupos.models.TransactionTenderData;
import com.trupos.services.TransactionService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

/**
 * Controller implementation to handle transaction requests
 */
@Slf4j
@RestController
@RequestMapping("/transactions")
@RequiredArgsConstructor
public class TransactionController {

    private final TransactionService transactionService;

    /**
     * Create a new transaction
     *
     * @param storeId Long
     * @param laneId Long
     * @param employeeId Long
     * @param customerId Long
     * @return ResponseEntity<TransactionDTO>
     */
    @Operation(summary = "Create a new transaction")
    @PostMapping
    public ResponseEntity<TransactionDTO> createTransaction(
            @Parameter(description = "Store ID") @RequestParam Long storeId,
            @Parameter(description = "Lane ID") @RequestParam Long laneId,
            @Parameter(description = "Employee ID") @RequestParam Long employeeId,
            @Parameter(description = "Customer ID") @RequestParam(required = false) Long customerId) {
        try {
            // Delegate creation of transaction to service
            Transaction transaction = transactionService.createTransaction(storeId, laneId, employeeId, customerId);
            TransactionDTO transactionDTO = new TransactionDTO(transaction);
            return new ResponseEntity<>(transactionDTO, HttpStatus.CREATED);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Get transaction by ID
     *
     * @param id Long
     * @return ResponseEntity<TransactionDTO>
     */
    @Operation(summary = "Get transaction by ID")
    @GetMapping("/{id}")
    public ResponseEntity<TransactionDTO> getTransactionById(
            @Parameter(description = "Transaction ID") @PathVariable Long id) {
        // Delegate fetching transaction to service
        Optional<Transaction> transaction = transactionService.getTransactionById(id);
        return transaction.map(t -> ResponseEntity.ok(new TransactionDTO(t)))
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Get transactions by store
     *
     * @param storeId Long
     * @param page int
     * @param size int
     * @param sortBy String
     * @param sortDir String
     * @return ResponseEntity<Page<Transaction>>
     */
    @Operation(summary = "Get transactions by store")
    @GetMapping("/store/{storeId}")
    public ResponseEntity<Page<Transaction>> getTransactionsByStore(
            @Parameter(description = "Store ID") @PathVariable Long storeId,
            @Parameter(description = "Page number") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "Sort by field") @RequestParam(defaultValue = "transactionTimestamp") String sortBy,
            @Parameter(description = "Sort direction") @RequestParam(defaultValue = "desc") String sortDir) {

        Sort sort = sortDir.equalsIgnoreCase("desc") ?
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);

        try {
            // Delegate fetching transactions for a store to service
            Page<Transaction> transactions = transactionService.getTransactionsByStore(storeId, pageable);
            return ResponseEntity.ok(transactions);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Add line item to transaction
     *
     * @param id Long
     * @param itemId Long
     * @param quantity BigDecimal
     * @param unitPrice BigDecimal
     * @return ResponseEntity<TransactionLineItemData>
     */
    @Operation(summary = "Add line item to transaction")
    @PostMapping("/{id}/line-items")
    public ResponseEntity<TransactionLineItemData> addLineItem(
            @Parameter(description = "Transaction ID") @PathVariable Long id,
            @Parameter(description = "Item ID") @RequestParam Long itemId,
            @Parameter(description = "Quantity") @RequestParam BigDecimal quantity,
            @Parameter(description = "Unit price") @RequestParam BigDecimal unitPrice) {
        try {
            // Delegate adding a line item to service
            TransactionLineItemData lineItem = transactionService.addLineItem(id, itemId, quantity, unitPrice);
            return ResponseEntity.ok(lineItem);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Add tender to transaction
     *
     * @param id Long
     * @param tenderType String
     * @param amount BigDecimal
     * @param cardLastFour String
     * @param authorizationCode String
     * @return ResponseEntity<TransactionTenderData>
     */
    @Operation(summary = "Add tender to transaction")
    @PostMapping("/{id}/tenders")
    public ResponseEntity<TransactionTenderData> addTender(
            @Parameter(description = "Transaction ID") @PathVariable Long id,
            @Parameter(description = "Tender type") @RequestParam String tenderType,
            @Parameter(description = "Amount") @RequestParam BigDecimal amount,
            @Parameter(description = "Card last four digits") @RequestParam(required = false) String cardLastFour,
            @Parameter(description = "Authorization code") @RequestParam(required = false) String authorizationCode) {
        try {
            // Delegate adding tender to service
            TransactionTenderData tender = transactionService.addTender(id, tenderType, amount, cardLastFour, authorizationCode);
            return ResponseEntity.ok(tender);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Complete transaction
     *
     * @param id Long
     * @return ResponseEntity<TransactionDTO>
     */
    @Operation(summary = "Complete transaction")
    @PutMapping("/{id}/complete")
    public ResponseEntity<TransactionDTO> completeTransaction(
            @Parameter(description = "Transaction ID") @PathVariable Long id) {
        try {
            // Delegate completion of transaction to service
            Transaction transaction = transactionService.completeTransaction(id);
            TransactionDTO transactionDTO = new TransactionDTO(transaction);
            return ResponseEntity.ok(transactionDTO);
        } catch (RuntimeException e) {
            System.err.println("ERROR: Controller - exception completing transaction: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Suspend transaction
     *
     * @param id Long
     * @return ResponseEntity<Transaction>
     */
    @Operation(summary = "Suspend transaction")
    @PutMapping("/{id}/suspend")
    public ResponseEntity<Transaction> suspendTransaction(
            @Parameter(description = "Transaction ID") @PathVariable Long id) {
        try {
            // Delegate suspending transaction to service
            Transaction transaction = transactionService.suspendTransaction(id);
            return ResponseEntity.ok(transaction);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Resume suspended transaction
     *
     * @param id Long
     * @return ResponseEntity<Transaction>
     */
    @Operation(summary = "Resume suspended transaction")
    @PutMapping("/{id}/resume")
    public ResponseEntity<Transaction> resumeTransaction(
            @Parameter(description = "Transaction ID") @PathVariable Long id) {
        try {
            // Delegate resuming transaction to service
            Transaction transaction = transactionService.resumeTransaction(id);
            return ResponseEntity.ok(transaction);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Void transaction
     *
     * @param id Long
     * @param voidReason String
     * @return ResponseEntity<Transaction>
     */
    @Operation(summary = "Void transaction")
    @PutMapping("/{id}/void")
    public ResponseEntity<Transaction> voidTransaction(
            @Parameter(description = "Transaction ID") @PathVariable Long id,
            @Parameter(description = "Void reason") @RequestParam String voidReason) {
        try {
            // Delegate voiding transaction to service
            Transaction transaction = transactionService.voidTransaction(id, voidReason);
            return ResponseEntity.ok(transaction);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Update transaction customer
     *
     * @param id Long
     * @param customerId Long
     * @return ResponseEntity<TransactionDTO>
     */
    @Operation(summary = "Update transaction customer")
    @PutMapping("/{id}/customer")
    public ResponseEntity<TransactionDTO> updateTransactionCustomer(
            @Parameter(description = "Transaction ID") @PathVariable Long id,
            @Parameter(description = "Customer ID") @RequestParam(required = false) Long customerId) {
        try {
            // Delegate updating transaction customer to service
            Transaction transaction = transactionService.updateTransactionCustomer(id, customerId);
            TransactionDTO transactionDTO = new TransactionDTO(transaction);
            return ResponseEntity.ok(transactionDTO);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Void line item
     *
     * @param transactionId Long
     * @param lineNumber Integer
     * @param voidReason String
     * @return ResponseEntity<Void>
     */
    @Operation(summary = "Void line item")
    @PutMapping("/{transactionId}/line-items/{lineNumber}/void")
    public ResponseEntity<Void> voidLineItem(
            @Parameter(description = "Transaction ID") @PathVariable Long transactionId,
            @Parameter(description = "Line number") @PathVariable Integer lineNumber,
            @Parameter(description = "Void reason") @RequestParam String voidReason) {
        try {
            // Delegate voiding line item to service
            transactionService.voidLineItem(transactionId, lineNumber, voidReason);
            return ResponseEntity.ok().build();
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Get suspended transactions by lane
     *
     * @param laneId Long
     * @return ResponseEntity<List<Transaction>>
     */
    @Operation(summary = "Get suspended transactions by lane")
    @GetMapping("/suspended/lane/{laneId}")
    public ResponseEntity<List<Transaction>> getSuspendedTransactionsByLane(
            @Parameter(description = "Lane ID") @PathVariable Long laneId) {
        try {
            // Delegate fetching suspended transactions by lane to service
            List<Transaction> transactions = transactionService.getSuspendedTransactionsByLane(laneId);
            return ResponseEntity.ok(transactions);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Get completed transactions by store
     *
     * @param storeId Long
     * @param page int
     * @param size int
     * @param sortBy String
     * @param sortDir String
     * @return ResponseEntity<Page<TransactionDTO>>
     */
    @Operation(summary = "Get completed transactions by store")
    @GetMapping("/completed/store/{storeId}")
    public ResponseEntity<Page<TransactionDTO>> getCompletedTransactionsByStore(
            @Parameter(description = "Store ID") @PathVariable Long storeId,
            @Parameter(description = "Page number") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "Sort by field") @RequestParam(defaultValue = "transactionTimestamp") String sortBy,
            @Parameter(description = "Sort direction") @RequestParam(defaultValue = "desc") String sortDir) {

        try {
            Sort sort = sortDir.equalsIgnoreCase("desc") ?
                    Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
            Pageable pageable = PageRequest.of(page, size, sort);

            // Delegate fetching completed transactions by store to service
            Page<Transaction> transactions = transactionService.getCompletedTransactionsByStore(storeId, pageable);
            Page<TransactionDTO> transactionDTOs = transactions.map(TransactionDTO::new);
            return ResponseEntity.ok(transactionDTOs);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Calculate total sales for date range
     *
     * @param storeId Long
     * @param startDate LocalDateTime
     * @param endDate LocalDateTime
     * @return ResponseEntity<BigDecimal>
     */
    @Operation(summary = "Calculate total sales for date range")
    @GetMapping("/sales-total")
    public ResponseEntity<BigDecimal> calculateTotalSales(
            @Parameter(description = "Store ID") @RequestParam Long storeId,
            @Parameter(description = "Start date") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @Parameter(description = "End date") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        try {
            // Delegate calculating total sales to service
            BigDecimal totalSales = transactionService.calculateTotalSales(storeId, startDate, endDate);
            return ResponseEntity.ok(totalSales);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().build();
        }
    }
}