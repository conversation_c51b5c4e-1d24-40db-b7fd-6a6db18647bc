package com.trupos.controllers;

import java.util.List;
import jakarta.validation.Valid;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.trupos.dtos.EmployeeDTO;
import com.trupos.models.LoginRequest;
import com.trupos.services.EmployeeService;

/**
 * Controller implementation to handle employee requests
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/employees")
public class EmployeeController {

    private final EmployeeService employeeService;

    /**
     * Process an employee creation request
     *
     * @param payload EmployeeDTO
     * @return ResponseEntity<EmployeeDTO>
     */
    @PostMapping
    public ResponseEntity<EmployeeDTO> create(@Valid @RequestBody EmployeeDTO payload) {

        log.info("create() -> payload: {}", payload);

        // Delegate saving employee to service
        EmployeeDTO dto = employeeService.save(payload);

        // Return OK with Employee data
        log.info("create() -> dto: {}", dto);
        return ResponseEntity.status(HttpStatus.CREATED).body(dto);
    }

    /**
     * Fetch an employee by ID
     *
     * @param id Long
     * @return ResponseEntity<EmployeeDTO>
     */
    @GetMapping("/{id}")
    public ResponseEntity<EmployeeDTO> getById(@PathVariable Long id) {

        log.info("getById() -> id: {}", id);

        // Delegate fetching employee to service
        EmployeeDTO dto = employeeService.findById(id);

        // Return OK with Employee data
        log.info("getById() -> dto: {}", dto);
        return ResponseEntity.status(HttpStatus.OK).body(dto);
    }

    /**
     * Update an employee
     *
     * @param id Long
     * @param payload EmployeeDTO
     * @return ResponseEntity<EmployeeDTO>
     */
    @PutMapping("/{id}")
    public ResponseEntity<EmployeeDTO> update(@PathVariable Long id, @Valid @RequestBody EmployeeDTO payload) {

        log.info("update() -> id: {}, payload: {}", id, payload);

        // Delegate updating employee to service
        EmployeeDTO dto = employeeService.update(id, payload);

        // Return OK with Employee data
        log.info("update() -> dto: {}", dto);
        return ResponseEntity.status(HttpStatus.OK).body(dto);
    }

    /**
     * Authorize a manager
     *
     * @param payload LoginRequest
     * @return ResponseEntity<EmployeeDTO>
     */
    @PostMapping("/authorize-manager")
    public ResponseEntity<EmployeeDTO> authorizeManager(@RequestBody LoginRequest payload) {

        log.info("authorizeManager() -> payload: {}", payload);

        // Delegate authorization of manager to service
        EmployeeDTO dto = employeeService.authorizeManager(payload.getLoginId(), payload.getPassword());

        // Return OK with Employee data
        log.info("authorizeManager() -> dto: {}", dto);
        return ResponseEntity.status(HttpStatus.OK).body(dto);
    }

    /**
     * Fetch employees by store
     *
     * @param storeId Long
     * @return ResponseEntity<List<EmployeeDTO>>
     */
    @GetMapping("/store/{storeId}")
    public ResponseEntity<List<EmployeeDTO>> getByStore(@PathVariable Long storeId) {

        log.info("getByStore() -> storeId: {}", storeId);

        // Delegate fetching employees to service
        List<EmployeeDTO> DTOs = employeeService.findByStore(storeId);

        // Return OK with Employee data
        log.info("getByStore() -> DTOs: {}", DTOs);
        return ResponseEntity.status(HttpStatus.OK).body(DTOs);
    }

    /**
     * Fetch active employees
     *
     * @return ResponseEntity<List<EmployeeDTO>>
     */
    @GetMapping("/active")
    public ResponseEntity<List<EmployeeDTO>> getActive() {

        log.info("getActive()");

        // Delegate fetching active employees to service
        List<EmployeeDTO> DTOs = employeeService.findActive();

        // Return OK with Employee data
        log.info("getActive() -> DTOs: {}", DTOs);
        return ResponseEntity.status(HttpStatus.OK).body(DTOs);
    }

    /**
     * Fetch active employees by store
     *
     * @param storeId Long
     * @return ResponseEntity<List<EmployeeDTO>>
     */
    @GetMapping("/active/store/{storeId}")
    public ResponseEntity<List<EmployeeDTO>> getActiveByStore(@PathVariable Long storeId) {

        log.info("getActiveByStore() -> storeId: {}", storeId);

        // Delegate fetching active employees by store to service
        List<EmployeeDTO> DTOs = employeeService.findActiveByStore(storeId);

        // Return OK with Employee data
        log.info("getActiveByStore() -> DTOs: {}", DTOs);
        return ResponseEntity.status(HttpStatus.OK).body(DTOs);
    }

    /**
     * Fetch managers
     *
     * @return ResponseEntity<List<EmployeeDTO>>
     */
    @GetMapping("/managers")
    public ResponseEntity<List<EmployeeDTO>> getManagers() {

        log.info("getManagers()");

        // Delegate fetching managers to service
        List<EmployeeDTO> managers = employeeService.findManagers();

        // Return OK with Employee data
        log.info("getManagers() -> managers: {}", managers);
        return ResponseEntity.status(HttpStatus.OK).body(managers);
    }

    /**
     * Search employees
     *
     * @param q String
     * @return ResponseEntity<List<EmployeeDTO>>
     */
    @GetMapping("/search")
    public ResponseEntity<List<EmployeeDTO>> search(@RequestParam String q) {

        log.info("search() -> q: {}", q);

        // Delegate searching employees to service
        List<EmployeeDTO> DTOs = employeeService.searchByName(q);

        // Return OK with Employee data
        log.info("search() -> DTOs: {}", DTOs);
        return ResponseEntity.status(HttpStatus.OK).body(DTOs);
    }

    /**
     * Activate an employee
     *
     * @param id Long
     * @return ResponseEntity<EmployeeDTO>
     */
    @PutMapping("/{id}/activate")
    public ResponseEntity<EmployeeDTO> activate(@PathVariable Long id) {

        log.info("activate() -> id: {}", id);

        // Delegate activating employee to service
        EmployeeDTO dto = employeeService.activate(id);

        // Return OK with Employee data
        log.info("activate() -> dto: {}", dto);
        return ResponseEntity.status(HttpStatus.OK).body(dto);
    }

    /**
     * Deactivate an employee
     *
     * @param id Long
     * @return ResponseEntity<EmployeeDTO>
     */
    @PutMapping("/{id}/deactivate")
    public ResponseEntity<EmployeeDTO> deactivate(@PathVariable Long id) {

        log.info("deactivate() -> id: {}", id);

        // Delegate deactivating employee to service
        EmployeeDTO dto = employeeService.deactivate(id);

        // Return OK with Employee data
        log.info("deactivate() -> dto: {}", dto);
        return ResponseEntity.status(HttpStatus.OK).body(dto);
    }

    /**
     * Assign a role to an employee
     *
     * @param id Long
     * @param roleId Long
     * @return ResponseEntity<EmployeeDTO>
     */
    @PutMapping("/{id}/role/{roleId}")
    public ResponseEntity<EmployeeDTO> assignRole(@PathVariable Long id, @PathVariable Long roleId) {

        log.info("assignRole() -> id: {}, roleId: {}", id, roleId);

        // Delegate assigning role to employee to service
        EmployeeDTO dto = employeeService.assignRole(id, roleId);

        // Return OK with Employee data
        log.info("assignRole() -> dto: {}", dto);
        return ResponseEntity.status(HttpStatus.OK).body(dto);
    }

    /**
     * Assign a store to an employee
     *
     * @param id Long
     * @param storeId Long
     * @return ResponseEntity<EmployeeDTO>
     */
    @PutMapping("/{id}/store/{storeId}")
    public ResponseEntity<EmployeeDTO> assignStore(@PathVariable Long id, @PathVariable Long storeId) {

        log.info("assignStore() -> id: {}, storeId: {}", id, storeId);

        // Delegate assigning store to employee to service
        EmployeeDTO dto = employeeService.assignStore(id, storeId);

        // Return OK with Employee data
        log.info("assignStore() -> dto: {}", dto);
        return ResponseEntity.status(HttpStatus.OK).body(dto);
    }
}