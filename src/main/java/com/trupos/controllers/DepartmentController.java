package com.trupos.controllers;

import java.util.List;
import jakarta.validation.Valid;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.trupos.dtos.DepartmentDTO;
import com.trupos.services.DepartmentService;

/**
 * Controller implementation to handle department requests
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/departments")
public class DepartmentController {

    private final DepartmentService departmentService;

    /**
     * Process a department creation request
     *
     * @param payload DepartmentDTO
     * @return ResponseEntity<DepartmentDTO>
     */
    @PostMapping
    public ResponseEntity<DepartmentDTO> create(@Valid @RequestBody DepartmentDTO payload) {

        log.info("create() -> payload: {}", payload);

        // Delegate department creation to service
        DepartmentDTO dto = departmentService.save(payload);

        // Return CREATED with Department data
        log.info("create() -> dto: {}", dto);
        return ResponseEntity.status(HttpStatus.CREATED).body(dto);
    }

    /**
     * Fetch all departments
     *
     * @return ResponseEntity<List<DepartmentDTO>>
     */
    @GetMapping
    public ResponseEntity<List<DepartmentDTO>> get() {

        log.info("get()");

        // Delegate fetching departments to service
        List<DepartmentDTO> DTOs = departmentService.findAll();

        // Return OK with Department data
        log.info("get() -> DTOs: {}", DTOs.size());
        return ResponseEntity.status(HttpStatus.OK).body(DTOs);
    }

    /**
     * Fetch all active departments
     *
     * @return ResponseEntity<List<DepartmentDTO>>
     */
    @GetMapping("/active")
    public ResponseEntity<List<DepartmentDTO>> getActive() {

        log.info("getActive()");

        // Delegate fetching active departments to service
        List<DepartmentDTO> DTOs = departmentService.findActive();

        // Return OK with Department data
        log.info("getActive() -> DTOs: {}", DTOs.size());
        return ResponseEntity.status(HttpStatus.OK).body(DTOs);
    }

    /**
     * Fetch department by ID
     *
     * @param id Long
     * @return ResponseEntity<DepartmentDTO>
     */
    @GetMapping("/{id}")
    public ResponseEntity<DepartmentDTO> getById(@PathVariable Long id) {

        log.info("getById() -> id: {}", id);

        // Delegate fetching department to service
        DepartmentDTO dto = departmentService.findById(id);

        // Return OK with Department data
        log.info("getById() -> dto: {}", dto);
        return ResponseEntity.status(HttpStatus.OK).body(dto);
    }

    /**
     * Fetch department by name
     *
     * @param name String
     * @return ResponseEntity<DepartmentDTO>
     */
    @GetMapping("/name/{name}")
    public ResponseEntity<DepartmentDTO> getByName(@PathVariable String name) {

        log.info("getByName() -> name: {}", name);

        // Delegate fetching department to service
        DepartmentDTO dto = departmentService.findByName(name);

        // Return OK with Department data
        log.info("getByName() -> dto: {}", dto);
        return ResponseEntity.status(HttpStatus.OK).body(dto);
    }

    /**
     * Update a department
     *
     * @param id Long
     * @param payload DepartmentDTO
     * @return ResponseEntity<DepartmentDTO>
     */
    @PutMapping("/{id}")
    public ResponseEntity<DepartmentDTO> update(@PathVariable Long id, @Valid @RequestBody DepartmentDTO payload) {

        log.info("update() -> id: {}, payload: {}", id, payload);

        // Delegate updating department to service
        DepartmentDTO dto = departmentService.update(id, payload);

        // Return OK with Department data
        log.info("update() -> dto: {}", dto);
        return ResponseEntity.status(HttpStatus.OK).body(dto);
    }

    /**
     * Delete a department by ID
     *
     * @param id Long
     * @return ResponseEntity<Void>
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> delete(@PathVariable Long id) {

        log.info("delete() -> id: {}", id);

        // Delegate deleting department to service
        departmentService.delete(id);

        // Return NO_CONTENT
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    /**
     * Search departments by keyword
     *
     * @param q String
     * @return ResponseEntity<List<DepartmentDTO>>
     */
    @GetMapping("/search")
    public ResponseEntity<List<DepartmentDTO>> search(@RequestParam String q) {

        log.info("search() -> query: {}", q);

        // Delegate searching departments to service
        List<DepartmentDTO> DTOs = departmentService.search(q);

        // Return OK with Department data
        log.info("search() -> DTOs: {}", DTOs.size());
        return ResponseEntity.ok(DTOs);
    }
}