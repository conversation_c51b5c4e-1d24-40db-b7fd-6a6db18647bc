package com.trupos.config;

import java.util.List;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;

@Configuration
public class OpenApiConfig {

    @Bean
    public OpenAPI truposOpenAPI() {
        Server devServer = new Server();
        devServer.setUrl("http://localhost:8080/api");
        devServer.setDescription("Development server");

        Contact contact = new Contact();
        contact.setEmail("<EMAIL>");
        contact.setName("TruPOS Support");
        contact.setUrl("https://www.trupos.com");

        License license = new License()
            .name("MIT License")
            .url("https://choosealicense.com/licenses/mit/");

        Info info = new Info()
            .title("TruPOS API")
            .version("1.0.0")
            .contact(contact)
            .description("REST API for TruPOS - Modern Point of Sale System")
            .termsOfService("https://www.trupos.com/terms")
            .license(license);

        return new OpenAPI()
            .info(info)
            .servers(List.of(devServer));
    }
}
