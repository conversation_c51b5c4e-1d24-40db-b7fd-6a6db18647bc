package com.trupos.config;

import lombok.RequiredArgsConstructor;

import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import com.trupos.entities.Employee;
import com.trupos.models.EmployeeDetails;
import com.trupos.repositories.EmployeeRepository;


/**
 * Custom implementation of Spring Security's UserDetailsService
 */
@Service
@RequiredArgsConstructor
public class EmployeeDetailsService implements UserDetailsService {
    private final EmployeeRepository employeeRepository;

    @Override
    public UserDetails loadUserByUsername(String loginId) throws UsernameNotFoundException {
        Employee employee = employeeRepository.findByLoginId(loginId)
                .orElseThrow(() -> new UsernameNotFoundException("Employee not found"));
        return new EmployeeDetails(employee);
    }
}