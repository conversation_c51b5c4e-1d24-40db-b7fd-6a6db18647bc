package com.trupos.models;

import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Data model for transaction tenders stored as JSON
 */
public class TransactionTenderData {
    
    @JsonProperty("tender_type")
    private String tenderType;
    
    @JsonProperty("amount")
    private BigDecimal amount;
    
    @JsonProperty("card_last_four")
    private String cardLastFour;
    
    @JsonProperty("authorization_code")
    private String authorizationCode;
    
    @JsonProperty("reference_number")
    private String referenceNumber;
    
    @JsonProperty("approval_code")
    private String approvalCode;
    
    @JsonProperty("currency")
    private String currency = "USD";
    
    @JsonProperty("tender_sequence")
    private Integer tenderSequence;

    // Default constructor
    public TransactionTenderData() {}

    // Constructor with essential fields
    public TransactionTenderData(String tenderType, BigDecimal amount) {
        this.tenderType = tenderType;
        this.amount = amount;
    }

    // Constructor with card details
    public TransactionTenderData(String tenderType, BigDecimal amount, String cardLastFour, String authorizationCode) {
        this.tenderType = tenderType;
        this.amount = amount;
        this.cardLastFour = cardLastFour;
        this.authorizationCode = authorizationCode;
    }

    // Getters and setters
    public String getTenderType() {
        return tenderType;
    }

    public void setTenderType(String tenderType) {
        this.tenderType = tenderType;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getCardLastFour() {
        return cardLastFour;
    }

    public void setCardLastFour(String cardLastFour) {
        this.cardLastFour = cardLastFour;
    }

    public String getAuthorizationCode() {
        return authorizationCode;
    }

    public void setAuthorizationCode(String authorizationCode) {
        this.authorizationCode = authorizationCode;
    }

    public String getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public String getApprovalCode() {
        return approvalCode;
    }

    public void setApprovalCode(String approvalCode) {
        this.approvalCode = approvalCode;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public Integer getTenderSequence() {
        return tenderSequence;
    }

    public void setTenderSequence(Integer tenderSequence) {
        this.tenderSequence = tenderSequence;
    }

    // Enum for tender types (for validation)
    public enum TenderType {
        CASH, CREDIT, DEBIT, CHECK, GIFT_CARD, STORE_CREDIT, MOBILE, WIC, EBT
    }
}
