package com.trupos.models;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.trupos.entity.Transaction;

/**
 * Complete transaction document for JSON storage
 * Contains all transaction data in a single document structure
 */
public class TransactionDocument {
    
    // Transaction metadata
    @JsonProperty("transaction_id")
    private Long transactionId;
    
    @JsonProperty("store_id")
    private Long storeId;
    
    @JsonProperty("lane_id")
    private Long laneId;
    
    @JsonProperty("employee_id")
    private Long employeeId;
    
    @JsonProperty("customer_id")
    private Long customerId;
    
    @JsonProperty("transaction_timestamp")
    private LocalDateTime transactionTimestamp;
    
    @JsonProperty("status")
    private Transaction.TransactionStatus status;
    
    // Financial totals
    @JsonProperty("subtotal")
    private BigDecimal subtotal;
    
    @JsonProperty("tax_amount")
    private BigDecimal taxAmount;
    
    @JsonProperty("total_amount")
    private BigDecimal totalAmount;
    
    @JsonProperty("discount_amount")
    private BigDecimal discountAmount;
    
    @JsonProperty("item_count")
    private Integer itemCount;
    
    // Operational flags
    @JsonProperty("is_training")
    private Boolean isTraining;
    
    @JsonProperty("is_voided")
    private Boolean isVoided;
    
    @JsonProperty("void_reason")
    private String voidReason;
    
    @JsonProperty("original_transaction_id")
    private Long originalTransactionId;
    
    // Transaction details
    @JsonProperty("line_items")
    private List<TransactionLineItemData> lineItems = new ArrayList<>();
    
    @JsonProperty("tenders")
    private List<TransactionTenderData> tenders = new ArrayList<>();
    
    // Store information (denormalized for reporting)
    @JsonProperty("store_info")
    private StoreInfo storeInfo;
    
    @JsonProperty("lane_info")
    private LaneInfo laneInfo;
    
    @JsonProperty("employee_info")
    private EmployeeInfo employeeInfo;
    
    @JsonProperty("customer_info")
    private CustomerInfo customerInfo;
    
    // Audit information
    @JsonProperty("created_at")
    private LocalDateTime createdAt;
    
    @JsonProperty("updated_at")
    private LocalDateTime updatedAt;
    
    @JsonProperty("completed_at")
    private LocalDateTime completedAt;
    
    // Tax calculation details
    @JsonProperty("tax_details")
    private TaxDetails taxDetails;
    
    // Payment details
    @JsonProperty("payment_summary")
    private PaymentSummary paymentSummary;
    
    // Default constructor
    public TransactionDocument() {}
    
    // Constructor from Transaction entity
    public TransactionDocument(Transaction transaction) {
        this.transactionId = transaction.getTransactionId();
        this.storeId = transaction.getStoreId();
        this.laneId = transaction.getLaneId();
        this.employeeId = transaction.getEmployeeId();
        this.customerId = transaction.getCustomerId();
        this.transactionTimestamp = transaction.getTransactionTimestamp();
        this.status = transaction.getStatus();
        this.subtotal = transaction.getSubtotal();
        this.taxAmount = transaction.getTaxAmount();
        this.totalAmount = transaction.getTotalAmount();
        this.discountAmount = transaction.getDiscountAmount();
        this.itemCount = transaction.getItemCount();
        this.isTraining = transaction.getIsTraining();
        this.isVoided = transaction.getIsVoided();
        this.voidReason = transaction.getVoidReason();
        this.originalTransactionId = transaction.getOriginalTransactionId();
        this.lineItems = transaction.getLineItems();
        this.tenders = transaction.getTenders();
        this.createdAt = transaction.getCreatedAt();
        this.updatedAt = transaction.getUpdatedAt();
        
        if (transaction.getStatus() == Transaction.TransactionStatus.COMPLETED) {
            this.completedAt = transaction.getUpdatedAt();
        }
    }
    
    // Nested classes for denormalized data
    public static class StoreInfo {
        @JsonProperty("store_id")
        private Long storeId;
        
        @JsonProperty("store_name")
        private String storeName;
        
        @JsonProperty("store_number")
        private Integer storeNumber;
        
        @JsonProperty("address")
        private String address;
        
        @JsonProperty("city")
        private String city;
        
        @JsonProperty("state")
        private String state;
        
        @JsonProperty("zip_code")
        private String zipCode;
        
        @JsonProperty("phone_number")
        private String phoneNumber;
        
        @JsonProperty("tax_rate")
        private BigDecimal taxRate;
        
        // Getters and setters
        public Long getStoreId() { return storeId; }
        public void setStoreId(Long storeId) { this.storeId = storeId; }
        public String getStoreName() { return storeName; }
        public void setStoreName(String storeName) { this.storeName = storeName; }
        public Integer getStoreNumber() { return storeNumber; }
        public void setStoreNumber(Integer storeNumber) { this.storeNumber = storeNumber; }
        public String getAddress() { return address; }
        public void setAddress(String address) { this.address = address; }
        public String getCity() { return city; }
        public void setCity(String city) { this.city = city; }
        public String getState() { return state; }
        public void setState(String state) { this.state = state; }
        public String getZipCode() { return zipCode; }
        public void setZipCode(String zipCode) { this.zipCode = zipCode; }
        public String getPhoneNumber() { return phoneNumber; }
        public void setPhoneNumber(String phoneNumber) { this.phoneNumber = phoneNumber; }
        public BigDecimal getTaxRate() { return taxRate; }
        public void setTaxRate(BigDecimal taxRate) { this.taxRate = taxRate; }
    }
    
    public static class LaneInfo {
        @JsonProperty("lane_id")
        private Long laneId;
        
        @JsonProperty("lane_number")
        private Integer laneNumber;
        
        @JsonProperty("lane_name")
        private String laneName;
        
        // Getters and setters
        public Long getLaneId() { return laneId; }
        public void setLaneId(Long laneId) { this.laneId = laneId; }
        public Integer getLaneNumber() { return laneNumber; }
        public void setLaneNumber(Integer laneNumber) { this.laneNumber = laneNumber; }
        public String getLaneName() { return laneName; }
        public void setLaneName(String laneName) { this.laneName = laneName; }
    }
    
    public static class EmployeeInfo {
        @JsonProperty("employee_id")
        private Long employeeId;
        
        @JsonProperty("login_id")
        private String loginId;
        
        @JsonProperty("first_name")
        private String firstName;
        
        @JsonProperty("last_name")
        private String lastName;
        
        // Getters and setters
        public Long getEmployeeId() { return employeeId; }
        public void setEmployeeId(Long employeeId) { this.employeeId = employeeId; }
        public String getLoginId() { return loginId; }
        public void setLoginId(String loginId) { this.loginId = loginId; }
        public String getFirstName() { return firstName; }
        public void setFirstName(String firstName) { this.firstName = firstName; }
        public String getLastName() { return lastName; }
        public void setLastName(String lastName) { this.lastName = lastName; }
    }
    
    public static class CustomerInfo {
        @JsonProperty("customer_id")
        private Long customerId;
        
        @JsonProperty("first_name")
        private String firstName;
        
        @JsonProperty("last_name")
        private String lastName;
        
        @JsonProperty("loyalty_card_number")
        private String loyaltyCardNumber;
        
        @JsonProperty("tax_exempt_id")
        private String taxExemptId;
        
        // Getters and setters
        public Long getCustomerId() { return customerId; }
        public void setCustomerId(Long customerId) { this.customerId = customerId; }
        public String getFirstName() { return firstName; }
        public void setFirstName(String firstName) { this.firstName = firstName; }
        public String getLastName() { return lastName; }
        public void setLastName(String lastName) { this.lastName = lastName; }
        public String getLoyaltyCardNumber() { return loyaltyCardNumber; }
        public void setLoyaltyCardNumber(String loyaltyCardNumber) { this.loyaltyCardNumber = loyaltyCardNumber; }
        public String getTaxExemptId() { return taxExemptId; }
        public void setTaxExemptId(String taxExemptId) { this.taxExemptId = taxExemptId; }
    }
    
    public static class TaxDetails {
        @JsonProperty("tax_rate")
        private BigDecimal taxRate;
        
        @JsonProperty("taxable_amount")
        private BigDecimal taxableAmount;
        
        @JsonProperty("tax_exempt")
        private Boolean taxExempt;
        
        @JsonProperty("tax_exempt_reason")
        private String taxExemptReason;
        
        // Getters and setters
        public BigDecimal getTaxRate() { return taxRate; }
        public void setTaxRate(BigDecimal taxRate) { this.taxRate = taxRate; }
        public BigDecimal getTaxableAmount() { return taxableAmount; }
        public void setTaxableAmount(BigDecimal taxableAmount) { this.taxableAmount = taxableAmount; }
        public Boolean getTaxExempt() { return taxExempt; }
        public void setTaxExempt(Boolean taxExempt) { this.taxExempt = taxExempt; }
        public String getTaxExemptReason() { return taxExemptReason; }
        public void setTaxExemptReason(String taxExemptReason) { this.taxExemptReason = taxExemptReason; }
    }
    
    public static class PaymentSummary {
        @JsonProperty("total_tendered")
        private BigDecimal totalTendered;
        
        @JsonProperty("change_due")
        private BigDecimal changeDue;
        
        @JsonProperty("payment_methods")
        private List<String> paymentMethods = new ArrayList<>();
        
        // Getters and setters
        public BigDecimal getTotalTendered() { return totalTendered; }
        public void setTotalTendered(BigDecimal totalTendered) { this.totalTendered = totalTendered; }
        public BigDecimal getChangeDue() { return changeDue; }
        public void setChangeDue(BigDecimal changeDue) { this.changeDue = changeDue; }
        public List<String> getPaymentMethods() { return paymentMethods; }
        public void setPaymentMethods(List<String> paymentMethods) { this.paymentMethods = paymentMethods; }
    }

    // Main class getters and setters
    public Long getTransactionId() { return transactionId; }
    public void setTransactionId(Long transactionId) { this.transactionId = transactionId; }

    public Long getStoreId() { return storeId; }
    public void setStoreId(Long storeId) { this.storeId = storeId; }

    public Long getLaneId() { return laneId; }
    public void setLaneId(Long laneId) { this.laneId = laneId; }

    public Long getEmployeeId() { return employeeId; }
    public void setEmployeeId(Long employeeId) { this.employeeId = employeeId; }

    public Long getCustomerId() { return customerId; }
    public void setCustomerId(Long customerId) { this.customerId = customerId; }

    public LocalDateTime getTransactionTimestamp() { return transactionTimestamp; }
    public void setTransactionTimestamp(LocalDateTime transactionTimestamp) { this.transactionTimestamp = transactionTimestamp; }

    public Transaction.TransactionStatus getStatus() { return status; }
    public void setStatus(Transaction.TransactionStatus status) { this.status = status; }

    public BigDecimal getSubtotal() { return subtotal; }
    public void setSubtotal(BigDecimal subtotal) { this.subtotal = subtotal; }

    public BigDecimal getTaxAmount() { return taxAmount; }
    public void setTaxAmount(BigDecimal taxAmount) { this.taxAmount = taxAmount; }

    public BigDecimal getTotalAmount() { return totalAmount; }
    public void setTotalAmount(BigDecimal totalAmount) { this.totalAmount = totalAmount; }

    public BigDecimal getDiscountAmount() { return discountAmount; }
    public void setDiscountAmount(BigDecimal discountAmount) { this.discountAmount = discountAmount; }

    public Integer getItemCount() { return itemCount; }
    public void setItemCount(Integer itemCount) { this.itemCount = itemCount; }

    public Boolean getIsTraining() { return isTraining; }
    public void setIsTraining(Boolean isTraining) { this.isTraining = isTraining; }

    public Boolean getIsVoided() { return isVoided; }
    public void setIsVoided(Boolean isVoided) { this.isVoided = isVoided; }

    public String getVoidReason() { return voidReason; }
    public void setVoidReason(String voidReason) { this.voidReason = voidReason; }

    public Long getOriginalTransactionId() { return originalTransactionId; }
    public void setOriginalTransactionId(Long originalTransactionId) { this.originalTransactionId = originalTransactionId; }

    public List<TransactionLineItemData> getLineItems() { return lineItems; }
    public void setLineItems(List<TransactionLineItemData> lineItems) { this.lineItems = lineItems; }

    public List<TransactionTenderData> getTenders() { return tenders; }
    public void setTenders(List<TransactionTenderData> tenders) { this.tenders = tenders; }

    public StoreInfo getStoreInfo() { return storeInfo; }
    public void setStoreInfo(StoreInfo storeInfo) { this.storeInfo = storeInfo; }

    public LaneInfo getLaneInfo() { return laneInfo; }
    public void setLaneInfo(LaneInfo laneInfo) { this.laneInfo = laneInfo; }

    public EmployeeInfo getEmployeeInfo() { return employeeInfo; }
    public void setEmployeeInfo(EmployeeInfo employeeInfo) { this.employeeInfo = employeeInfo; }

    public CustomerInfo getCustomerInfo() { return customerInfo; }
    public void setCustomerInfo(CustomerInfo customerInfo) { this.customerInfo = customerInfo; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }

    public LocalDateTime getCompletedAt() { return completedAt; }
    public void setCompletedAt(LocalDateTime completedAt) { this.completedAt = completedAt; }

    public TaxDetails getTaxDetails() { return taxDetails; }
    public void setTaxDetails(TaxDetails taxDetails) { this.taxDetails = taxDetails; }

    public PaymentSummary getPaymentSummary() { return paymentSummary; }
    public void setPaymentSummary(PaymentSummary paymentSummary) { this.paymentSummary = paymentSummary; }
}
