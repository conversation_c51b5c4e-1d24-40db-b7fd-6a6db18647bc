package com.trupos.models;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;

import lombok.Data;

/**
 * Encapsulates search parameters for paginated queries
 */
@Data
public class SearchDetails {

    private String q;

    @Min(value = 0, message = "Page must be 0 or greater")
    private int page = 0;

    @Min(value = 1, message = "Size must be at least 1")
    private int size = 20;

    @NotBlank(message = "Sort field is required")
    private String sortBy = "lastName";

    @NotBlank(message = "Sort direction is required")
    private String sortDir = "asc";
}
