package com.trupos.models;

import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Data model for transaction line items stored as JSON
 */
public class TransactionLineItemData {
    
    @JsonProperty("line_number")
    private Integer lineNumber;
    
    @JsonProperty("item_id")
    private Long itemId;
    
    @JsonProperty("item_description")
    private String itemDescription;
    
    @JsonProperty("upc")
    private String upc;
    
    @JsonProperty("quantity")
    private BigDecimal quantity;
    
    @JsonProperty("unit_price")
    private BigDecimal unitPrice;
    
    @JsonProperty("extended_price")
    private BigDecimal extendedPrice;
    
    @JsonProperty("discount_amount")
    private BigDecimal discountAmount = BigDecimal.ZERO;
    
    @JsonProperty("tax_amount")
    private BigDecimal taxAmount = BigDecimal.ZERO;
    
    @JsonProperty("tax_rate")
    private BigDecimal taxRate = BigDecimal.ZERO;
    
    @JsonProperty("is_voided")
    private Boolean isVoided = false;
    
    @JsonProperty("void_reason")
    private String voidReason;
    
    @JsonProperty("weight")
    private BigDecimal weight;
    
    @JsonProperty("line_item_type")
    private String lineItemType = "SALE";
    
    @JsonProperty("serial_number")
    private String serialNumber;
    
    @JsonProperty("sequence_number")
    private Integer sequenceNumber;
    
    @JsonProperty("is_taxable")
    private Boolean isTaxable = true;
    
    @JsonProperty("is_food_stampable")
    private Boolean isFoodStampable = false;
    
    @JsonProperty("is_wic_eligible")
    private Boolean isWicEligible = false;

    // Default constructor
    public TransactionLineItemData() {}

    // Constructor with essential fields
    public TransactionLineItemData(Integer lineNumber, Long itemId, String itemDescription, 
                                  BigDecimal quantity, BigDecimal unitPrice) {
        this.lineNumber = lineNumber;
        this.itemId = itemId;
        this.itemDescription = itemDescription;
        this.quantity = quantity;
        this.unitPrice = unitPrice;
        this.extendedPrice = quantity.multiply(unitPrice);
    }

    // Getters and setters
    public Integer getLineNumber() {
        return lineNumber;
    }

    public void setLineNumber(Integer lineNumber) {
        this.lineNumber = lineNumber;
    }

    public Long getItemId() {
        return itemId;
    }

    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    public String getItemDescription() {
        return itemDescription;
    }

    public void setItemDescription(String itemDescription) {
        this.itemDescription = itemDescription;
    }

    public String getUpc() {
        return upc;
    }

    public void setUpc(String upc) {
        this.upc = upc;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
        if (this.unitPrice != null) {
            this.extendedPrice = quantity.multiply(this.unitPrice);
        }
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
        if (this.quantity != null) {
            this.extendedPrice = this.quantity.multiply(unitPrice);
        }
    }

    public BigDecimal getExtendedPrice() {
        return extendedPrice;
    }

    public void setExtendedPrice(BigDecimal extendedPrice) {
        this.extendedPrice = extendedPrice;
    }

    public BigDecimal getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(BigDecimal discountAmount) {
        this.discountAmount = discountAmount;
    }

    public BigDecimal getTaxAmount() {
        return taxAmount;
    }

    public void setTaxAmount(BigDecimal taxAmount) {
        this.taxAmount = taxAmount;
    }

    public BigDecimal getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }

    public Boolean getIsVoided() {
        return isVoided;
    }

    public void setIsVoided(Boolean isVoided) {
        this.isVoided = isVoided;
    }

    public String getVoidReason() {
        return voidReason;
    }

    public void setVoidReason(String voidReason) {
        this.voidReason = voidReason;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public String getLineItemType() {
        return lineItemType;
    }

    public void setLineItemType(String lineItemType) {
        this.lineItemType = lineItemType;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public Integer getSequenceNumber() {
        return sequenceNumber;
    }

    public void setSequenceNumber(Integer sequenceNumber) {
        this.sequenceNumber = sequenceNumber;
    }

    public Boolean getIsTaxable() {
        return isTaxable;
    }

    public void setIsTaxable(Boolean isTaxable) {
        this.isTaxable = isTaxable;
    }

    public Boolean getIsFoodStampable() {
        return isFoodStampable;
    }

    public void setIsFoodStampable(Boolean isFoodStampable) {
        this.isFoodStampable = isFoodStampable;
    }

    public Boolean getIsWicEligible() {
        return isWicEligible;
    }

    public void setIsWicEligible(Boolean isWicEligible) {
        this.isWicEligible = isWicEligible;
    }

    // Helper methods
    @JsonIgnore
    public BigDecimal getFinalPrice() {
        return extendedPrice.subtract(discountAmount != null ? discountAmount : BigDecimal.ZERO);
    }

    @JsonIgnore
    public BigDecimal getTotalWithTax() {
        return getFinalPrice().add(taxAmount != null ? taxAmount : BigDecimal.ZERO);
    }
}
