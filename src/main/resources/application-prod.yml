# Production Profile - PostgreSQL Database Configuration
spring:
  datasource:
    url: ***************************************
    username: ${DB_USERNAME:trupos_user}
    password: ${DB_PASSWORD:trupos_password}
    driver-class-name: org.postgresql.Driver
    
    # Connection pooling for production
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 20000
      idle-timeout: 300000
      max-lifetime: 1200000
      leak-detection-threshold: 60000

  jpa:
    hibernate:
      ddl-auto: validate  # Don't auto-create/update schema in production
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true

# Production logging - less verbose
logging:
  level:
    "[com.trupos]": INFO
    "[org.springframework.security]": WARN
    "[org.hibernate.SQL]": WARN
    root: INFO
  file:
    name: logs/trupos-api-prod.log
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  logback:
    rollingpolicy:
      max-file-size: 50MB
      max-history: 90
      total-size-cap: 5GB
    
# Production-specific settings
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics  # Limited actuator endpoints in production
        
# Stricter CORS for production
cors:
  allowed-origins: 
    - https://yourdomain.com
    - https://pos.yourdomain.com
  allowed-methods:
    - GET
    - POST
    - PUT
    - DELETE
    - OPTIONS
  allowed-headers: 
    - Content-Type
    - Authorization
    - X-Requested-With
  allow-credentials: true
