# Development Profile - H2 Database Configuration
spring:
  datasource:
    url: jdbc:h2:mem:trupos_dev
    username: sa
    password:
    driver-class-name: org.h2.Driver

  h2:
    console:
      enabled: true
      path: /h2-console
      settings:
        web-allow-others: true

  jpa:
    hibernate:
      ddl-auto: create-drop  # Recreate schema on restart for development
    database-platform: org.hibernate.dialect.H2Dialect
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect

# Development logging - more verbose
logging:
  level:
    "[com.trupos]": DEBUG
    "[org.springframework.security]": DEBUG
    "[org.hibernate.SQL]": DEBUG
    "[org.hibernate.type.descriptor.sql.BasicBinder]": TRACE
  file:
    name: logs/trupos-api-dev.log
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    console: "%clr(%d{HH:mm:ss.SSS}){faint} %clr([%thread]){blue} %clr(%-5level){cyan} %clr(%logger{36}){magenta} - %msg%n"
    
# Development-specific settings
management:
  endpoints:
    web:
      exposure:
        include: "*"  # Expose all actuator endpoints in dev
        
# CORS settings for development
cors:
  allowed-origins: 
    - http://localhost:5173
    - http://localhost:3000
    - http://localhost:8080
  allowed-methods:
    - GET
    - POST
    - PUT
    - DELETE
    - OPTIONS
    - PATCH
  allowed-headers: "*"
  allow-credentials: true
