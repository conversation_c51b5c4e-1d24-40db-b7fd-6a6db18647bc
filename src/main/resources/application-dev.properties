# H2 In-memory database configuration for DEV
spring.datasource.url=jdbc:h2:mem:trupos;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect

# Use your custom SQL scripts for tables/data instead of Hibernate's ddl-auto
spring.jpa.hibernate.ddl-auto=none
spring.datasource.initialization-mode=always

# Load schema/data
spring.sql.init.schema-locations=classpath:schema.sql
spring.sql.init.data-locations=classpath:data.sql

# Enable H2 console
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

jwt.secret=FH/tnLkYNPd6LHQJlV8KJqoD5c8UUFM5Z9woAsCZmTE=
