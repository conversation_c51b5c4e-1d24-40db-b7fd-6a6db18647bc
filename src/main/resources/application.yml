spring:
  application:
    name: trupos-api

  # Default profile (can be overridden)
  profiles:
    active: dev

  jpa:
    # Common JPA settings for all databases
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        enable_lazy_load_no_trans: true

  jackson:
    serialization:
      fail-on-empty-beans: false
      write-dates-as-timestamps: false
    
  security:
    user:
      name: admin
      password: admin
      
server:
  port: 8080
  servlet:
    context-path: /api
    
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
        
logging:
  level:
    "[com.trupos]": INFO
    "[org.springframework.security]": WARN
    "[org.hibernate.SQL]": WARN
  file:
    name: logs/trupos-api.log
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  logback:
    rollingpolicy:
      max-file-size: 10MB
      max-history: 30
      total-size-cap: 1GB
    
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    
cors:
  allowed-origins: 
    - http://localhost:5173
    - http://localhost:3000
  allowed-methods:
    - GET
    - POST
    - PUT
    - DELETE
    - OPTIONS
  allowed-headers: "*"
  allow-credentials: true
