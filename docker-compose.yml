version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    container_name: trupos-postgres
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/setup-postgres.sql:/docker-entrypoint-initdb.d/setup-postgres.sql
    networks:
      - trupos-network

  # pgAdmin for database management (optional)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: trupos-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
    ports:
      - "5050:80"
    depends_on:
      - postgres
    networks:
      - trupos-network

  # TruPOS API with H2 (development)
  trupos-api-dev:
    build: .
    container_name: trupos-api-dev
    environment:
      - SPRING_PROFILES_ACTIVE=dev
    ports:
      - "8080:8080"
    networks:
      - trupos-network

  # TruPOS API with PostgreSQL (production)
  trupos-api-prod:
    build: .
    container_name: trupos-api-prod
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DB_USERNAME=trupos_user
      - DB_PASSWORD=trupos_password
    depends_on:
      - postgres
    ports:
      - "8081:8080"
    networks:
      - trupos-network

volumes:
  postgres_data:

networks:
  trupos-network:
    driver: bridge
