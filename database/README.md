# TruPOS Database

Database management and schema for the TruPOS point of sale system.

## 🗄️ Database Overview

### Technology Stack
- **H2 Database** - Development and testing
- **PostgreSQL** - Staging and production
- **Spring Data JPA** - ORM and data access
- **Flyway** - Database migrations (planned)

### Database Structure
```
TruPOS Database
├── stores              # Store information
├── lanes               # POS terminals/lanes
├── departments         # Product departments
├── items               # Product catalog
├── item_prices         # Store-specific pricing
├── customers           # Customer data
├── employees           # Staff and authentication
├── roles               # User roles
├── permissions         # Role permissions
├── transactions        # Sales transactions
├── transaction_line_items  # Items in transactions
└── transaction_tenders     # Payment methods
```

## 🏗️ Schema Design

### Core Entities

#### Store Management
- `stores` - Store locations and configuration
- `lanes` - Individual POS terminals within stores

#### Product Catalog
- `departments` - Product categorization
- `items` - Product master data
- `item_prices` - Store-specific pricing

#### Customer Management
- `customers` - Customer information and loyalty data

#### Employee Management
- `employees` - Staff information and authentication
- `roles` - User role definitions
- `permissions` - Granular permission system
- `role_permissions` - Role-permission mapping

#### Transaction Processing
- `transactions` - Sales transaction headers
- `transaction_line_items` - Individual items sold
- `transaction_tenders` - Payment methods used

### Key Relationships
```sql
stores (1) → (n) lanes
stores (1) → (n) item_prices
departments (1) → (n) items
items (1) → (n) item_prices
items (1) → (n) transaction_line_items
customers (1) → (n) transactions
employees (1) → (n) transactions
lanes (1) → (n) transactions
transactions (1) → (n) transaction_line_items
transactions (1) → (n) transaction_tenders
roles (1) → (n) employees
roles (n) ↔ (n) permissions
```

## 🔧 Development Setup

### H2 Database (Development)
```yaml
# application.yml
spring:
  datasource:
    url: jdbc:h2:mem:trupos
    username: sa
    password: 
  h2:
    console:
      enabled: true
      path: /h2-console
```

Access H2 Console: `http://localhost:8080/h2-console`

### PostgreSQL (Production)
```yaml
# application-production.yml
spring:
  datasource:
    url: ***************************************
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
```

## 📊 Sample Data

### Initial Data Loading
Sample data is loaded via `data.sql` for development:
- Default store and lane
- Sample departments and products
- Test customers and employees
- Demo transactions

### Data Seeding Scripts
Located in `scripts/`:
- `setup-postgres.sql` - PostgreSQL setup
- `sample-data.sql` - Sample data for testing
- `performance-data.sql` - Large dataset for performance testing

## 🔄 Database Operations

### Backup and Restore
```bash
# PostgreSQL backup
pg_dump -h localhost -U trupos_user trupos > backup.sql

# PostgreSQL restore
psql -h localhost -U trupos_user trupos < backup.sql

# H2 backup (automatic in development)
# Files stored in data/leveldb/
```

### Schema Updates
```bash
# Apply schema changes (manual for now)
psql -h localhost -U trupos_user trupos < migration.sql

# Future: Flyway migrations
mvn flyway:migrate
```

## 📈 Performance Optimization

### Indexes
Key indexes for performance:
```sql
-- Customer lookup
CREATE INDEX idx_customers_email ON customers(email);
CREATE INDEX idx_customers_phone ON customers(phone);

-- Product lookup
CREATE INDEX idx_items_upc ON items(upc);
CREATE INDEX idx_items_department ON items(department_id);

-- Transaction queries
CREATE INDEX idx_transactions_date ON transactions(created_at);
CREATE INDEX idx_transactions_customer ON transactions(customer_id);
```

### Query Optimization
- Use JPA projections for large result sets
- Implement pagination for list endpoints
- Cache frequently accessed data
- Use database views for complex reports

## 🔒 Security

### Data Protection
- Sensitive data encryption at rest
- PII data handling compliance
- Audit trails for data changes
- Role-based data access

### Access Control
- Database user permissions
- Application-level security
- Connection pooling limits
- Query timeout configuration

## 🧪 Testing

### Test Databases
- H2 in-memory for unit tests
- PostgreSQL for integration tests
- Test data isolation between tests

### Data Validation
- Entity validation annotations
- Database constraints
- Business rule validation
- Data integrity checks

## 📊 Monitoring

### Database Health
- Connection pool monitoring
- Query performance tracking
- Disk space monitoring
- Backup verification

### Metrics
- Transaction volume
- Response times
- Error rates
- Resource utilization

## 🔄 Migration Strategy

### H2 to PostgreSQL
1. Export schema from H2
2. Convert to PostgreSQL syntax
3. Migrate data with validation
4. Update connection configuration
5. Test thoroughly before production

### Version Control
- Schema changes tracked in Git
- Migration scripts versioned
- Rollback procedures documented
- Environment consistency maintained

## 🤝 Team Collaboration

### Database Changes
1. Create migration script
2. Test on development database
3. Review with team
4. Apply to staging
5. Deploy to production

### Data Access Patterns
- Repository pattern for data access
- Service layer for business logic
- DTO pattern for API responses
- Caching strategy for performance

## 📚 Documentation

### Schema Documentation
- Entity relationship diagrams
- Table and column descriptions
- Business rule documentation
- Data dictionary

### Operational Procedures
- Backup and restore procedures
- Performance tuning guidelines
- Troubleshooting guides
- Disaster recovery plans

## 🔧 Tools and Scripts

### Database Scripts
- `setup-postgres.sql` - Initial PostgreSQL setup
- `sample-data.sql` - Development sample data
- `performance-test-data.sql` - Performance testing data
- `cleanup-old-data.sql` - Data archival

### Monitoring Scripts
- `check-db-health.sql` - Database health check
- `performance-report.sql` - Performance metrics
- `space-usage.sql` - Storage analysis

## 📞 Support

### Common Issues
- Connection timeout solutions
- Performance optimization tips
- Data corruption recovery
- Migration troubleshooting

### Team Contacts
- Database Team: Responsible for schema design and optimization
- Backend Team: Coordinate for entity changes
- DevOps Team: Coordinate for deployment and monitoring
