# TruPOS Database Configuration Guide

TruPOS supports seamless switching between H2 (development) and PostgreSQL (production) databases using Spring Boot profiles.

## 🎯 Quick Start

### Development (H2 Database)
```bash
# Start with H2 - no setup required
./scripts/start-dev.sh
```
- ✅ **No external dependencies**
- ✅ **Fast startup**
- ✅ **H2 Console**: http://localhost:8080/api/h2-console
- ✅ **Auto-creates schema**

### Production (PostgreSQL Database)
```bash
# Setup PostgreSQL first
docker-compose up postgres -d

# Start with PostgreSQL
./scripts/start-prod.sh
```
- ✅ **Enterprise-grade reliability**
- ✅ **Persistent data**
- ✅ **Production-ready**

## 📊 Database Profiles

| Profile | Database | Use Case | DDL Mode | Logging |
|---------|----------|----------|----------|---------|
| `dev` | H2 | Development | create-drop | DEBUG |
| `staging` | PostgreSQL | Testing | update | INFO |
| `prod` | PostgreSQL | Production | validate | WARN |

## 🔧 Manual Profile Switching

### Command Line
```bash
# H2 Development
java -jar trupos-api.jar --spring.profiles.active=dev

# PostgreSQL Production
java -jar trupos-api.jar --spring.profiles.active=prod

# PostgreSQL Staging
java -jar trupos-api.jar --spring.profiles.active=staging
```

### Environment Variable
```bash
export SPRING_PROFILES_ACTIVE=prod
java -jar trupos-api.jar
```

### Maven
```bash
./mvnw spring-boot:run -Dspring-boot.run.profiles=dev
./mvnw spring-boot:run -Dspring-boot.run.profiles=prod
```

## 🐘 PostgreSQL Setup

### Option 1: Docker (Recommended)
```bash
# Start PostgreSQL with automatic database setup
docker-compose up postgres -d

# View logs
docker-compose logs postgres

# Access pgAdmin (optional)
# URL: http://localhost:5050
# Email: <EMAIL>
# Password: admin
```

### Option 2: Local Installation
```bash
# Install PostgreSQL
brew install postgresql  # macOS
sudo apt install postgresql  # Ubuntu

# Start PostgreSQL service
brew services start postgresql  # macOS
sudo systemctl start postgresql  # Ubuntu

# Run setup script
psql -U postgres -f scripts/setup-postgres.sql
```

## 🔐 Database Credentials

### Default Credentials
| Environment | Database | Username | Password |
|-------------|----------|----------|----------|
| Development | H2 | sa | (empty) |
| Staging | PostgreSQL | trupos_staging | staging_password |
| Production | PostgreSQL | trupos_user | trupos_password |

### Environment Variables
```bash
# Override PostgreSQL credentials
export DB_USERNAME=your_username
export DB_PASSWORD=your_password
```

## 🔍 Database Access

### H2 Console (Development)
- **URL**: http://localhost:8080/api/h2-console
- **JDBC URL**: jdbc:h2:mem:trupos_dev
- **Username**: sa
- **Password**: (leave empty)

### pgAdmin (PostgreSQL)
- **URL**: http://localhost:5050
- **Email**: <EMAIL>
- **Password**: admin

## 🚀 API Documentation

All profiles expose the same API endpoints:
- **Swagger UI**: http://localhost:8080/api/swagger-ui.html
- **API Docs**: http://localhost:8080/api/api-docs

## 🔄 Zero Code Changes

The same codebase works with both databases:
- ✅ All Entity classes unchanged
- ✅ All Repository interfaces unchanged
- ✅ All Service classes unchanged
- ✅ All Controller classes unchanged

## 🛠️ Troubleshooting

### PostgreSQL Connection Issues
```bash
# Check if PostgreSQL is running
nc -z localhost 5432

# View PostgreSQL logs
docker-compose logs postgres

# Restart PostgreSQL
docker-compose restart postgres
```

### Profile Not Loading
```bash
# Check active profile
curl http://localhost:8080/api/actuator/env | grep "spring.profiles.active"

# Force profile
export SPRING_PROFILES_ACTIVE=dev
```

### Schema Issues
```bash
# Development: Schema auto-recreated
# Staging: Schema auto-updated
# Production: Schema must exist (use migrations)
```

## 📈 Performance Comparison

| Feature | H2 | PostgreSQL |
|---------|----|-----------| 
| Startup Time | ~2 seconds | ~5 seconds |
| Memory Usage | Low | Moderate |
| Query Performance | Fast (in-memory) | Excellent (optimized) |
| Concurrent Users | Limited | High |
| Data Persistence | None | Full |
| Production Ready | No | Yes |

## 🎯 Recommendations

- 🏠 **Local Development**: Use H2 for fast iteration
- 🧪 **Testing**: Use PostgreSQL staging for realistic testing
- 🏢 **Production**: Use PostgreSQL for reliability and performance
- 🔄 **CI/CD**: Use H2 for fast test execution
