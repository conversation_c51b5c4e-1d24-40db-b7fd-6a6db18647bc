-- PostgreSQL Database Setup for TruPOS
-- Run this script as a PostgreSQL superuser (e.g., postgres)

-- Create databases
CREATE DATABASE trupos;
CREATE DATABASE trupos_staging;

-- Create users
CREATE USER trupos_user WITH PASSWORD 'trupos_password';
CREATE USER trupos_staging WITH PASSWORD 'staging_password';

-- Grant privileges for production database
GRANT ALL PRIVILEGES ON DATABASE trupos TO trupos_user;
\c trupos;
GRANT ALL ON SCHEMA public TO trupos_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO trupos_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO trupos_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO trupos_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO trupos_user;

-- Grant privileges for staging database
\c trupos_staging;
GRANT ALL PRIVILEGES ON <PERSON>AT<PERSON>ASE trupos_staging TO trupos_staging;
GRANT ALL ON SCHEMA public TO trupos_staging;
G<PERSON>NT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO trupos_staging;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO trupos_staging;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO trupos_staging;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO trupos_staging;

-- Display created databases and users
\l
\du
