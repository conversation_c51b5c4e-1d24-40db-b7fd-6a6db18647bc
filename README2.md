# TruPOS Backend

Spring Boot REST API for the TruPOS point of sale system.

## 🚀 Quick Start

```bash
# Build the project
mvn clean install

# Start development server
mvn spring-boot:run

# Run tests
mvn test

# Package for production
mvn clean package
```

## 🏗️ Architecture

### Technology Stack
- **Spring Boot 3.2.1** - Application framework
- **Java 17** - Programming language
- **Spring Data JPA** - Data access layer
- **H2 Database** - Development database
- **PostgreSQL** - Production database
- **Maven** - Build tool
- **Swagger/OpenAPI** - API documentation

### Project Structure
```
backend/
├── src/
│   ├── main/java/com/trupos/
│   │   ├── controller/     # REST controllers
│   │   ├── service/        # Business logic
│   │   ├── repository/     # Data access
│   │   ├── entity/         # JPA entities
│   │   ├── dto/            # Data transfer objects
│   │   ├── config/         # Configuration classes
│   │   └── model/          # Domain models
│   └── main/resources/
│       ├── application.yml # Configuration
│       └── data.sql        # Sample data
├── target/                 # Build artifacts
├── scripts/                # Backend-specific scripts
└── logs/                   # Application logs
```

## 🔧 Development

### Environment Setup
1. Ensure Java 17+ is installed
2. Ensure Maven 3.6+ is installed
3. Configure database connection in `application.yml`
4. Run `mvn spring-boot:run` to start

### Available Commands
- `mvn spring-boot:run` - Start development server
- `mvn clean package` - Build JAR file
- `mvn test` - Run tests
- `mvn clean install` - Clean build and install
- `mvn spring-boot:build-image` - Build Docker image

### Database Configuration
- **Development**: H2 in-memory database
- **Staging**: PostgreSQL
- **Production**: PostgreSQL

### API Documentation
Available at: `http://localhost:8080/swagger-ui.html`

## 🗄️ Database

### Entity Relationships
- **Store** → **Lane** → **Transaction**
- **Customer** ← **Transaction** → **Employee**
- **Product** → **TransactionLineItem** ← **Transaction**
- **Department** ← **Product**

### Key Entities
- `Store` - Store information
- `Lane` - POS terminal/lane
- `Customer` - Customer data
- `Employee` - Staff and authentication
- `Product` - Product catalog
- `Transaction` - Sales transactions
- `TransactionLineItem` - Individual items in transaction

## 🔌 API Endpoints

### Customer Management
- `GET /api/customers` - List customers
- `GET /api/customers/{id}` - Get customer
- `POST /api/customers` - Create customer
- `PUT /api/customers/{id}` - Update customer
- `DELETE /api/customers/{id}` - Delete customer

### Product Management
- `GET /api/products` - List products
- `GET /api/products/{id}` - Get product
- `GET /api/products/upc/{upc}` - Get by UPC
- `POST /api/products` - Create product
- `PUT /api/products/{id}` - Update product

### Transaction Management
- `POST /api/transactions` - Create transaction
- `GET /api/transactions/{id}` - Get transaction
- `POST /api/transactions/{id}/line-items` - Add item
- `POST /api/transactions/{id}/tenders` - Add payment
- `PUT /api/transactions/{id}/complete` - Complete transaction

### Employee Management
- `POST /api/employees/authenticate` - Login
- `POST /api/employees/authorize-manager` - Manager auth
- `GET /api/employees` - List employees
- `POST /api/employees` - Create employee

## 🧪 Testing

```bash
# Run all tests
mvn test

# Run specific test class
mvn test -Dtest=CustomerControllerTest

# Run tests with coverage
mvn test jacoco:report
```

## 🔒 Security

### Authentication
- Session-based authentication
- Role-based access control (RBAC)
- Manager authorization for sensitive operations

### CORS Configuration
- Configured for frontend applications
- Customizable via `application.yml`
- Default allows common development ports

## 📦 Building & Deployment

### Development Build
```bash
mvn spring-boot:run
```

### Production Build
```bash
mvn clean package
java -jar target/trupos-api-0.0.1-SNAPSHOT.jar
```

### Docker Build
```bash
mvn spring-boot:build-image
docker run -p 8080:8080 trupos-api:0.0.1-SNAPSHOT
```

## 🔧 Configuration

### Profiles
- `default` - H2 database, development settings
- `staging` - PostgreSQL, staging settings
- `production` - PostgreSQL, production settings

### Environment Variables
- `DB_URL` - Database connection URL
- `DB_USERNAME` - Database username
- `DB_PASSWORD` - Database password
- `CORS_ORIGINS` - Allowed CORS origins

## 📊 Monitoring

### Health Checks
- Available at: `http://localhost:8080/actuator/health`
- Database connectivity
- Application status

### Logging
- Configurable via `logback-spring.xml`
- Separate log files for different components
- Log rotation and archival

## 🤝 Team Collaboration

### Code Style
- Java coding standards enforced
- Maven checkstyle plugin
- SonarQube integration

### Database Changes
- Use Flyway migrations for schema changes
- Coordinate with database team
- Test migrations on staging first

### API Changes
- Update OpenAPI documentation
- Coordinate with frontend team
- Maintain backward compatibility when possible

## 📚 Documentation

- [API Documentation](http://localhost:8080/swagger-ui.html)
- [Database Schema](database/README.md)
- [Deployment Guide](../docs/deployment/)
- [Security Guide](./docs/security.md)
