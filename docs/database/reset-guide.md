# Database Reset Guide

This guide provides methods to reset the TruPOS backend database when experiencing issues or for development purposes.

## When to Reset the Database

Reset the database if you experience:
- Database connection issues
- Corrupted data or schema problems
- Development testing requires a clean slate
- Migration issues between database versions
- Performance issues due to test data accumulation

## ⚠️ Important Warning

**Resetting the database will permanently delete all data including:**
- All transactions and sales data
- Customer information
- Product catalog
- Employee records
- Store configuration
- All business data

**Always backup important data before proceeding.**

## Method 1: H2 Database Reset (Development)

For development using H2 in-memory database:

1. Stop the backend application
2. Restart the application - H2 data is automatically cleared
3. Or use the reset profile:

```bash
cd backend
mvn spring-boot:run -Dspring-boot.run.profiles=dev -Dspring.jpa.hibernate.ddl-auto=create-drop
```

## Method 2: H2 Console Reset

If using H2 with file persistence:

1. Access H2 Console: http://localhost:8080/h2-console
2. Login with:
   - JDBC URL: `jdbc:h2:mem:testdb` (or your configured URL)
   - User Name: `sa`
   - Password: (leave blank)
3. Run SQL commands to drop and recreate tables:

```sql
-- Drop all tables (order matters due to foreign keys)
DROP TABLE IF EXISTS transaction_line_items;
DROP TABLE IF EXISTS transactions;
DROP TABLE IF EXISTS products;
DROP TABLE IF EXISTS customers;
DROP TABLE IF EXISTS employees;
DROP TABLE IF EXISTS departments;
DROP TABLE IF EXISTS stores;
DROP TABLE IF EXISTS lanes;

-- Tables will be recreated automatically on next startup
```

## Method 3: PostgreSQL Database Reset

For PostgreSQL database reset:

1. Connect to PostgreSQL:
```bash
# Using Docker
cd backend
docker-compose exec postgres psql -U postgres -d trupos

# Or direct connection
psql -h localhost -U postgres -d trupos
```

2. Drop and recreate the database:
```sql
-- Connect to postgres database first
\c postgres

-- Drop the trupos database
DROP DATABASE IF EXISTS trupos;

-- Recreate the database
CREATE DATABASE trupos;

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE trupos TO postgres;
```

3. Restart the backend application to recreate tables

## Method 4: Application Properties Reset

Reset via application configuration:

1. Stop the backend application
2. Temporarily modify `backend/src/main/resources/application.yml`:

```yaml
spring:
  jpa:
    hibernate:
      ddl-auto: create-drop  # This will drop and recreate tables
```

3. Start the application
4. Stop the application
5. Change `ddl-auto` back to `update` or `validate`
6. Restart the application

## Method 5: Docker Reset (PostgreSQL)

If using Docker for PostgreSQL:

```bash
cd backend

# Stop and remove containers
docker-compose down

# Remove volumes (this deletes all data)
docker-compose down -v

# Start fresh
docker-compose up postgres
```

## Preventing Future Database Issues

To minimize database problems:

1. Use proper transaction management in code
2. Regularly backup production databases
3. Use database migrations for schema changes
4. Monitor database logs for errors
5. Keep database drivers and Spring Boot updated
6. Use connection pooling appropriately

## When to Reset Databases

Consider resetting databases when:

- Schema changes cause migration issues
- Test data accumulation affects performance
- Database corruption is detected
- Development environment needs clean state
- Integration tests require fresh data

**Note:** Only reset development databases. Production databases should be backed up and restored, not reset.

## Database Backup and Restore

Before resetting, consider backing up:

### H2 Database Backup
```bash
# Backup H2 database files
cp backend/data/trupos.* backup/
```

### PostgreSQL Backup
```bash
# Create backup
pg_dump -h localhost -U postgres trupos > backup/trupos_backup.sql

# Restore backup
psql -h localhost -U postgres trupos < backup/trupos_backup.sql
```

## Additional Support

If you continue to experience database issues:
1. Check backend logs for specific error messages
2. Verify database connection settings
3. Ensure proper database permissions
4. Contact technical support or file an issue in the project repository