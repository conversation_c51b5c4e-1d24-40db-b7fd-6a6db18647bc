# TruPOS API Documentation

The TruPOS API provides comprehensive endpoints for point-of-sale operations, inventory management, customer relations, and business analytics.

## API Overview

- **Base URL**: `http://localhost:8080/api`
- **Format**: JSON
- **Authentication**: Session-based (planned: JWT tokens)
- **Documentation**: Swagger UI at `http://localhost:8080/swagger-ui.html`

## Quick Start

1. **Start Backend**: `./scripts/development/start-trupos-backend.sh`
2. **Import Postman Collection**: `tools/postman/TruPOS_Enhanced_API_Collection.postman_collection.json`
3. **Set Environment**: Base URL = `http://localhost:8080/api`
4. **Test Connection**: GET `/api/health` (if available)

## Core API Modules

### 🏪 Store Management
- **Stores**: Create and manage store locations
- **Lanes**: Configure checkout lanes
- **Employees**: Manage staff and permissions

### 🛍️ Product & Inventory
- **Products**: CRUD operations for items
- **Search**: Find products by UPC, SKU, name
- **Pricing**: Get current prices and promotions
- **Categories**: Organize products by department

### 👤 Customer Management
- **Customers**: Create and manage customer accounts
- **Loyalty**: Handle loyalty card operations
- **Search**: Find customers by various criteria

### 🛒 Transaction Processing
- **Transactions**: Create and manage sales transactions
- **Line Items**: Add/remove items from transactions
- **Payments**: Process various payment methods
- **Receipts**: Generate and print receipts

### 📊 Reporting & Analytics
- **Sales Reports**: Daily, weekly, monthly sales data
- **Inventory Reports**: Stock levels and movement
- **Customer Analytics**: Purchase patterns and loyalty metrics

## Authentication

### Employee Authentication
```http
POST /api/employees/authenticate
Content-Type: application/json

{
  "loginId": "manager01",
  "password": "manager123"
}
```

### Response
```json
{
  "employeeId": 1,
  "firstName": "John",
  "lastName": "Manager",
  "role": "MANAGER",
  "storeId": 1,
  "isActive": true
}
```

## Common Workflows

### 1. Complete Transaction Flow

```http
# 1. Create Transaction
POST /api/transactions
{
  "storeId": 1,
  "laneId": 1,
  "employeeId": 1
}

# 2. Add Line Items
POST /api/transactions/{transactionId}/line-items
{
  "itemId": 1,
  "quantity": 2,
  "unitPrice": 1.99
}

# 3. Add Customer (optional)
PUT /api/transactions/{transactionId}/customer/{customerId}

# 4. Process Payment
POST /api/transactions/{transactionId}/payments
{
  "tenderType": "CASH",
  "amount": 10.00
}

# 5. Complete Transaction
PUT /api/transactions/{transactionId}/complete
```

### 2. Product Search & Lookup

```http
# Search by name
GET /api/products/search?q=apple&active=true

# Find by UPC/barcode
GET /api/products/upc/123456789012

# Find by SKU
GET /api/products/sku/APPLE-001

# Get current price
GET /api/products/{productId}/price?storeId=1
```

### 3. Customer Operations

```http
# Search customers
GET /api/customers/search?q=john

# Find by loyalty card
GET /api/customers/loyalty/1234567890

# Create new customer
POST /api/customers
{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "phoneNumber": "************"
}
```

## Response Formats

### Success Response
```json
{
  "success": true,
  "data": {
    // Response data
  },
  "message": "Operation completed successfully"
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "code": "PRODUCT_NOT_FOUND",
    "message": "Product with UPC 123456789012 not found",
    "details": {}
  }
}
```

## Status Codes

- **200 OK**: Successful operation
- **201 Created**: Resource created successfully
- **400 Bad Request**: Invalid request data
- **404 Not Found**: Resource not found
- **500 Internal Server Error**: Server error

## Rate Limiting

Currently no rate limiting is implemented. For production deployment, consider implementing rate limiting based on:
- IP address
- User/session
- API endpoint

## Testing

### Postman Collections

1. **Enhanced Collection**: `tools/postman/TruPOS_Enhanced_API_Collection.postman_collection.json`
   - Complete business workflows
   - Real-world scenarios
   - Automated testing scripts

2. **Basic Collection**: `tools/postman/TruPOS_API_Collection.json`
   - Individual endpoint testing
   - Basic CRUD operations

### Environment Variables

Set these in your Postman environment:
```json
{
  "baseUrl": "http://localhost:8080/api",
  "storeId": "1",
  "laneId": "1",
  "employeeId": "1",
  "transactionId": "",
  "customerId": "",
  "productId": ""
}
```

### Automated Testing

```bash
# Run with Newman CLI
newman run tools/postman/TruPOS_Enhanced_API_Collection.postman_collection.json \
  --environment tools/postman/environments/local.json
```

## API Versioning

Currently using URL versioning:
- **v1**: `/api/v1/...` (planned)
- **Current**: `/api/...` (no version prefix)

## Security Considerations

### Current Implementation
- Basic session-based authentication
- Input validation on all endpoints
- SQL injection protection via JPA

### Planned Enhancements
- JWT token authentication
- Role-based access control (RBAC)
- API key authentication for integrations
- Request signing for sensitive operations

## Integration Examples

### JavaScript/Frontend
```javascript
const response = await fetch('/api/products/active');
const products = await response.json();
```

### cURL
```bash
curl -X GET "http://localhost:8080/api/products/active" \
  -H "Content-Type: application/json"
```

### Python
```python
import requests

response = requests.get('http://localhost:8080/api/products/active')
products = response.json()
```

## Support

- **API Issues**: Check Postman collections for examples
- **Documentation**: See [testing guide](testing-guide.md)
- **Backend Logs**: `backend/backend.log`
- **Development**: Use Swagger UI for interactive testing

## Migration Guide

See [migration-guide.md](migration-guide.md) for information about API changes and migration between versions.
