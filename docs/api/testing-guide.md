# TruPOS Enhanced API Testing Guide

## 🚀 Overview

This comprehensive Postman collection provides real-world API calls for testing your TruPOS system. It includes everything you need to simulate actual Point of Sale operations, from finding sellable items to processing complete transactions.

## 📁 Collection Structure

### 🏪 **Store Setup & Management**
- Create sample stores for testing
- Configure basic store information

### 👥 **Employee Management**
- Create manager and cashier accounts
- Employee authentication and authorization
- Search and manage employees by store

### 🛍️ **Product & Inventory Management**

#### 🔍 **Product Discovery** (Your Key Request!)
- **Get All Active Products (Sellable Items)** - Find everything you can sell
- **Search Products by Name/Description** - Quick product lookup
- **Find Product by UPC/Barcode** - Scan to find products
- **Find Product by SKU** - Internal product lookup

#### 🏷️ **Special Categories**
- **Get Scale Items** - Products that need weighing (produce, deli)
- **Get Age Restricted Items** - Alcohol, tobacco, etc.
- **Get WIC Eligible Items** - Government assistance program items
- **Check Age Verification** - Verify if ID is required

#### 💰 **Pricing**
- **Get Current Price** - Real-time pricing for any product

#### 📦 **Product Management**
- Create, update, activate/deactivate products

### 👤 **Customer Management**

#### 🔍 **Customer Lookup**
- Search customers by name, phone, email
- Find customers by loyalty card scan
- Check tax exemption status

#### 👤 **Customer Management**
- Create new customer accounts
- Get customer statistics

### 🛒 **Transaction Processing**

#### 💳 **POS Operations**
- **Start New Transaction** - Begin checkout process
- **Start Transaction with Customer** - Attach customer to sale
- **Add Item to Transaction** - Scan/add products
- **Complete Transaction** - Finalize sale
- **Suspend/Resume Transaction** - Handle interruptions
- **Void Transaction** - Cancel with reason

#### 📊 **Transaction Reporting**
- Get transactions by store/lane
- Find suspended transactions
- Calculate sales totals for date ranges
- Detailed transaction lookup

### 🔧 **System Administration**

#### 📈 **Analytics & Reports**
- Customer counts and statistics
- Employee management reports
- Search functionality

#### 🏪 **Store Operations**
- Products by department
- Active employees by store

### 🧪 **Testing & Development**
- **Create Sample Data** - Pre-built test products
  - Sample scale item (bananas)
  - Sample age-restricted item (beer)

## 🎯 **Key Use Cases**

### **Finding Sellable Items** (Your Main Request)

1. **Get All Active Products**
   ```
   GET {{baseUrl}}/products/active
   ```
   Returns all products currently available for sale.

2. **Search for Specific Items**
   ```
   GET {{baseUrl}}/products/search?q=apple&active=true
   ```
   Find products by name or description.

3. **Barcode Scanning**
   ```
   GET {{baseUrl}}/products/upc/123456789012
   ```
   Look up products by scanning barcodes.

### **Complete POS Workflow**

1. **Start Transaction**
   ```
   POST {{baseUrl}}/transactions?storeId=1&laneId=1&employeeId=1
   ```

2. **Add Items**
   ```
   POST {{baseUrl}}/transactions/1/items
   Body: {"itemId": 1, "quantity": 2, "unitPrice": 1.99}
   ```

3. **Complete Sale**
   ```
   PUT {{baseUrl}}/transactions/1/complete
   ```

## 🔧 **Setup Instructions**

### 1. **Import Collection**
- Open Postman
- Click "Import"
- Select `TruPOS_Enhanced_API_Collection.postman_collection.json`

### 2. **Configure Variables**
The collection includes these variables you can customize:
- `baseUrl`: http://localhost:8080/api (default)
- `storeId`: 1 (default)
- `employeeId`: 1 (default)
- `laneId`: 1 (default)

### 3. **Create Test Data**
Run these requests in order to set up test data:

1. **Create Sample Store** (Store Setup & Management)
2. **Create Manager Employee** (Employee Management)
3. **Create Sample Products** (Testing & Development)

## 🎯 **Recommended Testing Flow**

### **Quick Setup (First Time)**
1. **Create Sample Store** (🏪 Store Setup & Management)
2. **Create Manager Employee** (👥 Employee Management)
3. **Create Sample Products** (🧪 Testing & Development → Create Sample Data)
4. **Create Sample Customer** (👤 Customer Management)

### **🔄 Complete Workflow Testing**

The collection now includes comprehensive workflow scenarios that mirror real POS operations:

#### **📋 Normal Transaction Flow**
This is a complete 7-step transaction process that you can run sequentially:

1. **Create Transaction** - Starts a new transaction and captures the transaction ID
2. **Add First Item** - Adds 2x $1.99 items to the transaction
3. **Add Second Item** - Adds 1x $3.49 item to the transaction
4. **Search and Add Customer** - Searches for a customer and captures their ID
5. **Add Customer to Transaction** - Associates the customer with the transaction
6. **Add Cash Tender** - Adds a $10.00 cash payment
7. **Complete Transaction** - Finalizes the transaction

Each step includes test scripts that:
- ✅ Confirm successful operations with green checkmarks
- ❌ Report failures with red X marks
- 📝 Log important details like IDs and amounts
- 🔗 Automatically pass data between requests using variables

#### **🛒 Product Lookup Scenarios**
- **Find Product by UPC/Barcode** - Simulates barcode scanning
- **Search Products by Name** - Simulates product search functionality

### **💡 How to Use Workflow Scenarios**

1. **Run Individual Steps**: Click any step in the "Normal Transaction Flow" to test specific operations
2. **Run Complete Flow**: Use Postman's "Run Collection" feature to execute all steps in sequence
3. **Monitor Progress**: Watch the console output for real-time feedback on each operation
4. **Automatic Variables**: Transaction IDs, Customer IDs, and Product IDs are automatically captured and reused

### **🔧 Variables Used**
The collection automatically manages these variables:
- `transactionId` - Captured when creating transactions
- `customerId` - Captured when searching customers
- `productId` - Captured when finding products
- `baseUrl`, `storeId`, `laneId`, `employeeId` - Configuration variables

## 🎯 **Legacy Testing Flow**

### **Phase 1: Setup**
1. Create sample store
2. Create manager and cashier employees
3. Create sample products (apple, banana, beer)

### **Phase 2: Product Discovery**
1. Get all active products
2. Search for specific items
3. Test barcode lookup
4. Check special categories (age-restricted, WIC, scale items)

### **Phase 3: Customer Management**
1. Create sample customers
2. Test customer lookup methods
3. Test loyalty card scanning

### **Phase 4: Transaction Processing**
1. Start a new transaction
2. Add multiple items
3. Test suspend/resume functionality
4. Complete transaction
5. Test void functionality

### **Phase 5: Reporting**
1. View transaction history
2. Calculate sales totals
3. Generate reports

## 🔍 **Special Features**

### **Real-World Scenarios**
- Age verification for alcohol/tobacco
- Weight-based pricing for produce
- WIC eligibility checking
- Tax exemption handling
- Loyalty card integration

### **Error Handling**
- Invalid product lookups
- Insufficient permissions
- Transaction state validation

### **Pagination & Sorting**
- Large result set handling
- Customizable page sizes
- Multiple sort options

## 🚨 **Important Notes**

1. **Security**: Current setup has permissive security for development
2. **Database**: Using H2 in-memory database (data resets on restart)
3. **Variables**: Update collection variables for your environment
4. **Dependencies**: Some requests depend on data from previous requests

## 🎉 **Quick Start**

### **Option 1: Test Individual Operations**
1. Import the collection
2. Run "Get All Active Products" to see sellable items
3. Run "Create Sample Product - Banana" to add test data
4. Run "Get All Active Products" again to see your new product

### **Option 2: Test Complete Transaction Flow**
1. Import the collection
2. Navigate to "🔄 Complete Workflow Scenarios" → "📋 Normal Transaction Flow"
3. Run each step in order (1-7) to simulate a complete POS transaction
4. Watch the console output for real-time feedback

### **Option 3: Automated Testing**
1. Import the collection
2. Use Postman's "Run Collection" feature
3. Select the "Normal Transaction Flow" folder
4. Click "Run" to execute all steps automatically

## 🔄 **New Workflow Features**

The enhanced collection now includes:

### **📋 Complete Transaction Scenarios**
- **Normal Transaction Flow**: 7-step process from creation to completion
- **Product Lookup Scenarios**: UPC scanning and name search workflows
- **Customer Management**: Search and add customers mid-transaction

### **🤖 Automated Variable Management**
- Transaction IDs automatically captured and reused
- Customer IDs passed between search and assignment
- Product IDs captured from lookup operations
- No manual ID copying required!

### **📊 Real-Time Feedback**
- Console logging shows success/failure for each step
- Detailed information about created resources
- Clear error messages when operations fail

### **🎯 UI-Matching Scenarios**
These workflows mirror the actual UI operations:
1. **Create transaction** (like clicking "New Sale")
2. **Add items** (like scanning products)
3. **Add customer** (like the customer search dialog)
4. **Process payment** (like the tender screen)
5. **Complete sale** (like finalizing the transaction)

This collection gives you everything you need to test your TruPOS system comprehensively!
