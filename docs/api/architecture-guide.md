# TruPOS API Architecture Guide

This document outlines the TruPOS API architecture and how the frontend and backend components work together.

## Architecture Overview

### System Architecture
```
React Frontend → REST API Client → SpringBoot Backend → H2/PostgreSQL Database
                     ↓
                NoSQL (Offline Cache)
```

## Architecture Components

### 1. SpringBoot Backend (`backend/`)
- **Location**: `backend/src/main/java/com/trupos/`
- **Technology**: Spring Boot 3.2.1, Java 17
- **Database**: H2 (development) / PostgreSQL (production)
- **API Documentation**: Swagger/OpenAPI at `http://localhost:8080/swagger-ui.html`

#### Key Components:
- **Entities**: JPA entities representing database tables
- **Repositories**: Spring Data JPA repositories for data access
- **Services**: Business logic layer
- **Controllers**: REST API endpoints
- **Security**: Spring Security configuration with CORS support

### 2. Frontend API Client (`frontend/src/services/api/`)
- **apiClient.ts**: Axios-based HTTP client with interceptors
- **customerApi.ts**: Customer management API calls
- **productApi.ts**: Product/inventory management API calls
- **transactionApi.ts**: Transaction processing API calls
- **employeeApi.ts**: Employee authentication and management API calls

### 3. Connection Management (`frontend/src/services/connectionManager.ts`)
- Online/offline detection
- API health monitoring
- Automatic fallback to NoSQL when offline
- Retry logic with exponential backoff

## API Endpoints

### Customer Management
- `GET /api/customers/{id}` - Get customer by ID
- `GET /api/customers/search` - Search customers with pagination
- `POST /api/customers` - Create new customer
- `PUT /api/customers/{id}` - Update customer
- `DELETE /api/customers/{id}` - Soft delete customer
- `GET /api/customers/loyalty/{cardNumber}` - Get by loyalty card

### Product Management
- `GET /api/products/{id}` - Get product by ID
- `GET /api/products/upc/{upc}` - Get product by UPC/barcode
- `GET /api/products/search` - Search products with pagination
- `POST /api/products` - Create new product
- `PUT /api/products/{id}` - Update product
- `POST /api/products/{id}/price` - Set product price for store

### Transaction Management
- `POST /api/transactions` - Create new transaction
- `GET /api/transactions/{id}` - Get transaction by ID
- `POST /api/transactions/{id}/line-items` - Add line item
- `POST /api/transactions/{id}/tenders` - Add tender/payment
- `PUT /api/transactions/{id}/complete` - Complete transaction
- `PUT /api/transactions/{id}/suspend` - Suspend transaction

### Employee Management
- `POST /api/employees/authenticate` - Employee login
- `POST /api/employees/authorize-manager` - Manager authorization
- `GET /api/employees/active` - Get active employees
- `POST /api/employees` - Create new employee

## Development Setup

### 1. Backend Setup
```bash
cd backend
mvn spring-boot:run
```

The backend will start on `http://localhost:8080` with API documentation at `/swagger-ui.html`.

### 2. Database Setup (Optional - PostgreSQL)
```bash
cd backend
docker-compose up postgres
```

PostgreSQL will start on `localhost:5432` with pgAdmin available at `http://localhost:5050`.

### 3. Environment Configuration
Backend environment variables (for production):
```env
DB_URL=***************************************
DB_USERNAME=trupos_user
DB_PASSWORD=secure_password
CORS_ORIGINS=https://your-frontend-domain.com
```

### 4. Testing with Postman
Import the Postman collection from `tools/postman/TruPOS_API_Collection.json` to test all API endpoints.

## API Design Principles

The TruPOS backend follows REST API design principles:

1. **Resource-Based URLs**: Clear, hierarchical resource paths
2. **HTTP Methods**: Proper use of GET, POST, PUT, DELETE
3. **Status Codes**: Meaningful HTTP status codes for responses
4. **JSON Communication**: Consistent JSON request/response format

## Architecture Benefits

### 1. Separation of Concerns
- **Backend**: Business logic, validation, and data processing
- **Database**: Data persistence and integrity
- **API Layer**: Clean interface for frontend applications

### 2. Security
- Session-based authentication and authorization
- Input validation and sanitization
- Role-based access control (RBAC)
- Secure API endpoints with proper CORS configuration

### 3. Performance
- Optimized JPA queries with proper indexing
- Connection pooling for database access
- Efficient pagination for large datasets
- Caching strategies for frequently accessed data

### 4. Scalability
- Horizontal scaling of API servers
- Load balancing capabilities
- Microservices architecture potential
- Independent deployment cycles

### 5. Maintainability
- Clear API contracts with OpenAPI documentation
- Comprehensive Swagger documentation
- Type safety with Java strong typing
- Standardized error handling and responses

## Development Workflow

### 1. Backend Development
```bash
cd backend
mvn spring-boot:run
```

### 2. API Testing
- Use Swagger UI: `http://localhost:8080/swagger-ui.html`
- Import Postman collection: `tools/postman/TruPOS_API_Collection.json`
- Run automated tests: `mvn test`
- Test with curl commands

### 3. Database Management
- H2 Console (development): `http://localhost:8080/h2-console`
- PostgreSQL (production): `docker-compose up postgres`
- pgAdmin interface: `http://localhost:5050`
- Schema validation on startup
- Connection pooling configured automatically

### 4. Building and Deployment
```bash
# Development build
cd backend && mvn spring-boot:run

# Production build
cd backend && mvn clean package
java -jar target/trupos-api-0.0.1-SNAPSHOT.jar

# Docker build
cd backend && mvn spring-boot:build-image
```

## Troubleshooting

### Common Issues

1. **CORS Errors**
   - Ensure frontend URL is in `corsOrigins` configuration
   - Check browser developer tools for specific CORS messages

2. **Connection Timeouts**
   - Verify backend is running on correct port
   - Check firewall settings
   - Increase timeout values if needed

3. **Database Connection Issues**
   - Verify database connection string
   - Check database credentials
   - Ensure database is accessible from backend

4. **Authentication Failures**
   - Verify employee credentials
   - Check role and permission assignments
   - Review security configuration

### Monitoring and Logging

- **Backend Logs**: Available in console output and log files
- **API Health**: `GET /actuator/health`
- **Metrics**: `GET /actuator/metrics`
- **Application Info**: `GET /actuator/info`

## Future Enhancements

1. **Authentication Tokens**: JWT-based authentication
2. **Real-time Updates**: WebSocket support for live data
3. **Caching**: Redis integration for improved performance
4. **Monitoring**: Application performance monitoring with Micrometer
5. **Documentation**: Enhanced API documentation with examples
6. **Testing**: Comprehensive integration and unit tests
7. **Database Migration**: Flyway integration for schema versioning

## Support

For questions or issues related to the API:

1. Check the Swagger documentation: `http://localhost:8080/swagger-ui.html`
2. Review the Postman collection for API examples
3. Review backend logs for server-side issues
4. Check actuator endpoints for health and metrics
5. Use H2 console for database inspection (development)
