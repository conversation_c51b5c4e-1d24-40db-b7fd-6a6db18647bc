# TruPOS Documentation

This directory contains all project documentation organized by category.

## Documentation Structure

```
docs/
├── api/                    # API documentation
│   ├── README.md          # API overview
│   ├── migration-guide.md # API migration guide
│   └── testing-guide.md   # API testing guide
├── deployment/            # Deployment and installation
│   ├── installation.md   # Installation guide
│   └── docker.md         # Docker deployment
├── development/           # Development guides
│   ├── quick-start.md    # Quick start guide
│   ├── database.md       # Database setup and schema
│   ├── migration-guide.md # Project reorganization guide
│   └── contributing.md   # Contributing guidelines
└── user-guides/          # End-user documentation
    ├── pos-operations.md # POS system usage
    └── admin-guide.md    # Administrative functions
```

## Quick Links

- [Installation Guide](deployment/installation.md)
- [Quick Start](development/quick-start.md)
- [API Documentation](api/README.md)
- [Database Setup](development/database.md)
- [Migration Guide](development/migration-guide.md)
