# TruPOS Backend Quick Start Guide

## 🚀 Current Status
✅ **Backend is running** on http://localhost:8080/api

## 📱 Access the Application
Open your web browser and go to: **http://localhost:8080/swagger-ui.html**

## 🔧 Quick Start Options

### Option 1: Use the Startup Script (Recommended)
Use the startup script located in `scripts/development/`:

```bash
./scripts/development/start-backend.sh
```

### Option 2: Manual Commands
If you prefer to start services manually:

**Backend:**
```bash
cd backend
java -jar target/trupos-api-0.0.1-SNAPSHOT.jar
```

**Or with <PERSON>ven:**
```bash
cd backend
mvn spring-boot:run
```

## 🛑 Stopping Services

### If using scripts:
- Press `Ctrl+C` in the terminal where the script is running

### If running manually:
- Press `Ctrl+C` in each terminal window

### If services are running in background:
```bash
# Find and kill backend
lsof -ti:8080 | xargs kill
```

## 📊 Service Information

| Service | URL | Port | Status |
|---------|-----|------|--------|
| Backend API | http://localhost:8080/api | 8080 | ✅ Running |
| H2 Database Console | http://localhost:8080/h2-console | 8080 | ✅ Available |
| API Documentation | http://localhost:8080/swagger-ui.html | 8080 | ✅ Available |
| Health Check | http://localhost:8080/actuator/health | 8080 | ✅ Available |

## 🔍 Troubleshooting

### Backend won't start:
- Check if Java 17+ is installed: `java -version`
- Check if JAR file exists: `ls -la backend/target/trupos-api-0.0.1-SNAPSHOT.jar`
- If missing, build it: `cd backend && mvn clean package`
- Check if port 8080 is available: `lsof -i :8080`

### Database issues:
- Check H2 console: http://localhost:8080/h2-console
- For PostgreSQL: `cd backend && docker-compose up postgres`

### Port conflicts:
- Check what's using port 8080: `lsof -i :8080`

## 📝 Log Files
When using the startup scripts, logs are saved to:
- Backend: `logs/backend.log` or `backend/backend.log`

## 🎯 Next Steps
1. Open http://localhost:8080/swagger-ui.html in your browser
2. Test the API endpoints using Swagger UI
3. Explore the API documentation
4. Check out the development guides in `docs/development/`
5. Import Postman collection from `tools/postman/`

## 🔧 Development Mode

For development with hot reloading:

```bash
# Start backend in development mode
cd backend
mvn spring-boot:run

# Or with specific profile
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

## 🏗️ Building for Production

```bash
# Build backend
cd backend
mvn clean package

# Run production build
java -jar target/trupos-api-0.0.1-SNAPSHOT.jar
```

## 📚 Additional Resources

- **API Documentation**: [docs/api/](../api/)
- **Development Guide**: [docs/development/](../development/)
- **Database Guide**: [docs/database/](../database/)
- **Deployment Guide**: [docs/deployment/](../deployment/)

---
*For more detailed information, see the complete documentation in the `docs/` directory.*
