# TruPOS Backend - Installation Guide

This document provides instructions for installing the TruPOS Backend API on Windows and Linux operating systems.

## Prerequisites

Before installing, ensure you have:

- Java 17 or higher
- Maven 3.6 or higher
- PostgreSQL (for production) or H2 (for development)
- Internet connection for downloading dependencies

## Environment Variables

The backend application can be configured using environment variables or application.yml files. For production deployments, you may need to configure database connection settings.

Required environment variables:
- `VITE_SUPABASE_URL`: Your Supabase project URL
- `VITE_SUPABASE_ANON_KEY`: Your Supabase anonymous key
- `VITE_EBT_EDGE_API_URL`: EBT Edge API endpoint
- `VITE_EBT_EDGE_API_KEY`: Your EBT Edge API key
- `VITE_SCALE_WEBSOCKET_URL`: WebSocket URL for the scale integration (default: ws://localhost:8080)
- `VITE_PRINTER_WEBSOCKET_URL`: WebSocket URL for the printer integration (default: ws://localhost:9623)

## Windows Installation

1. **Download the Installation Files**
   - Ensure you have all the application files including `install-windows.bat`

2. **Run the Installation Script**
   - Double-click on `install-windows.bat` or run it from the command prompt
   - If a security warning appears, click "Run anyway" or "More info" > "Run anyway"

3. **Configure Environment Variables**
   - The script will create a `.env` file if it doesn't exist
   - Open the `.env` file in a text editor
   - Update the values with your actual configuration

4. **Follow the On-screen Instructions**
   - The script will:
     - Check if Node.js is installed
     - Install the required dependencies
     - Process environment variables
     - Build the application
     - Create a startup script

5. **Starting the Application**
   - After installation, you can start the application by running `start-trupos.bat`
   - The application will be accessible in your web browser at http://localhost:3000

## Linux Installation

1. **Download the Installation Files**
   - Ensure you have all the application files including `install-linux.sh`

2. **Make the Script Executable**
   ```bash
   chmod +x install-linux.sh
   ```

3. **Run the Installation Script**
   ```bash
   ./install-linux.sh
   ```

4. **Configure Environment Variables**
   - The script will create a `.env` file if it doesn't exist
   - Open the `.env` file in a text editor
   - Update the values with your actual configuration

5. **Follow the On-screen Instructions**
   - The script will:
     - Check if Node.js is installed
     - Install the required dependencies
     - Process environment variables
     - Build the application
     - Create a startup script and desktop entry

6. **Starting the Application**
   - After installation, you can start the application by running:
     ```bash
     ./start-trupos.sh
     ```
   - Or find "TRUPOS Grocery POS" in your application menu
   - The application will be accessible in your web browser at http://localhost:3000

## Troubleshooting

### Common Issues on Windows

- **'node' is not recognized as an internal or external command**
  - Solution: Install Node.js from https://nodejs.org/ and restart your computer

- **Access Denied Errors**
  - Solution: Right-click the installation script and select "Run as administrator"

- **Environment Variables Not Loading**
  - Solution: Make sure the `.env` file exists and has the correct format
  - Check that all required variables are set
  - Ensure there are no spaces around the `=` signs

### Common Issues on Linux

- **Permission Denied**
  - Solution: Make sure the script is executable using `chmod +x install-linux.sh`

- **Node.js Not Found**
  - Solution: Install Node.js using your distribution's package manager

- **Application Not Starting**
  - Solution: Check if `npx` is installed. If not, run `npm install -g npx`

- **Environment Variables Not Loading**
  - Solution: Check file permissions on `.env` file
  - Ensure the file is in the correct location
  - Verify the file format is correct

## Manual Installation

If the installation scripts don't work for your environment, you can manually install by following these steps:

1. Install Java 17+ from https://adoptium.net/
2. Install Maven 3.6+ from https://maven.apache.org/
3. Create environment variables for database configuration (if using PostgreSQL)
4. Open a terminal or command prompt in the backend directory
5. Run `mvn clean install` to build the application
6. Run `mvn spring-boot:run` to start the backend service
7. Access the API at http://localhost:8080/api

## Support

If you encounter any issues with the installation, please contact technical <NAME_EMAIL> or open an issue in our repository. 