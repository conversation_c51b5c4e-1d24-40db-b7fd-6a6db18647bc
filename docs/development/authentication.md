# Custom Employee Authentication

This document explains how to set up custom employee authentication using the `employee` and `permission` tables in the Postgres database instead of Supabase Auth.

## Overview

The application has been modified to:
1. Use the `employee` table for user authentication
2. Support numeric login IDs and passwords for touchscreen use
3. Implement bcrypt password verification
4. Set default password "123456" for all employees

## Implementation Steps

### 1. SQL Script to Set Default Passwords

Run the `set_default_employee_passwords.sql` script on your Postgres database:

```sql
-- Update all employees with the hashed password for '123456'
UPDATE employee
SET password_hash = '$2a$10$kUc3pXWAX3VTbfK8TvZfTeglz2sBu.0Nud3MiXXoWEa9YIR4mUbG.'
WHERE true;
```

This sets all employee passwords to "123456".

### 2. Verify Employee Data

Ensure all employees have:
- A numeric `login_id` value (for touchscreen use)
- A valid `role_id` linked to the `role` table
- The correct password hash

You can check employee data with:

```sql
SELECT 
  employee_id, 
  login_id, 
  first_name, 
  last_name, 
  role_id,
  SUBSTRING(password_hash, 1, 10) || '...' AS password_hash_preview
FROM employee;
```

### 3. JavaScript Modifications

The following files were modified:

1. `src/contexts/AuthContext.tsx`: Removed Supabase auth, added direct employee authentication
2. `src/pages/LoginPage.tsx`: Updated with numeric keypad for touchscreen input
3. `src/main.tsx`: Added AuthProvider to the React component tree

### 4. Password Management

- Default password for all accounts: `123456`
- To verify the password hash works properly, you can run:
  ```bash
  node verify_employee_password.js
  ```

### 5. Adding New Employees

When adding new employees:
1. Use numeric values for `login_id` (e.g., "1001", "1002")
2. Set the `password_hash` field with a bcrypt hash of the password
3. Assign the appropriate `role_id`

You can generate password hashes using bcrypt:

```javascript
const bcrypt = require('bcryptjs');

async function generateHash(password) {
  const salt = await bcrypt.genSalt(10);
  const hash = await bcrypt.hash(password, salt);
  console.log(`Password: ${password}`);
  console.log(`Hash: ${hash}`);
}

generateHash("123456"); // Replace with the desired password
```

## Authentication Flow

1. User enters numeric login ID and password
2. System looks up employee by login_id
3. System verifies password using bcrypt
4. On success, user's role permissions are loaded from the database
5. User is authenticated and session is stored in localStorage

## Troubleshooting

1. If users can't log in:
   - Verify their `login_id` exists in the employee table
   - Check that their password hash is correct
   - Ensure they have a valid `role_id`

2. To reset a user's password to the default:
   ```sql
   UPDATE employee
   SET password_hash = '$2a$10$kUc3pXWAX3VTbfK8TvZfTeglz2sBu.0Nud3MiXXoWEa9YIR4mUbG.'
   WHERE login_id = '1001'; -- Replace with the user's login_id
   ```

## Security Considerations

1. The default password should be changed after initial setup
2. In a production environment, consider implementing:
   - Password complexity requirements
   - Password expiration
   - Account lockout after failed attempts 