# TruPOS Grocery Database Schema

This document describes the database schema for the TruPOS Grocery application. The schema is designed for a retail point-of-sale system with support for multiple stores, lanes, products, customers, and transactions.

## Core Tables

### store

Stores information about each physical store location.

| Column | Type | Description |
|--------|------|-------------|
| store_id | SERIAL PRIMARY KEY | Unique store identifier |
| store_name | VARCHAR(255) | Name of the store |
| address | TEXT | Street address |
| city | VARCHAR(255) | City |
| state | VARCHAR(2) | State code (e.g., CA, NY) |
| zip_code | VARCHAR(10) | ZIP code |
| phone_number | VARCHAR(20) | Store phone number |
| tax_rate | NUMERIC(5, 4) | Default store tax rate |
| store_no | INT | Store number (for external reference) |

### lane

Represents a checkout lane within a store.

| Column | Type | Description |
|--------|------|-------------|
| lane_id | SERIAL PRIMARY KEY | Unique lane identifier |
| store_id | INTEGER REFERENCES store(store_id) | Foreign key to the store |
| lane_number | SMALLINT | Lane number within the store |
| lane_type | VARCHAR(50) | Type of lane (e.g., 'regular', 'express', 'self-checkout', 'service') |
| is_active | BOOLEAN | Whether the lane is currently operational |
| ip_address | VARCHAR(50) | Network address of the lane |
| last_sync_timestamp | TIMESTAMP | When the lane last synchronized with the central database |

### department

Categorizes products into departments.

| Column | Type | Description |
|--------|------|-------------|
| department_id | SERIAL PRIMARY KEY | Unique department identifier |
| department_name | VARCHAR(100) | Name of the department |

### item

Stores information about products sold in the store.

| Column | Type | Description |
|--------|------|-------------|
| item_id | SERIAL PRIMARY KEY | Unique item identifier |
| upc | VARCHAR(20) UNIQUE | Universal Product Code |
| sku | VARCHAR(50) | Stock Keeping Unit |
| description | VARCHAR(255) | Item description |
| department_id | INTEGER REFERENCES department(department_id) | Foreign key to the department |
| brand | VARCHAR(100) | Brand name |
| size | VARCHAR(50) | Size (e.g., "12 oz", "1 liter") |
| unit_of_measure | VARCHAR(20) | Unit of measure (e.g., "each", "lb", "kg", "oz") |
| is_taxable | BOOLEAN | Whether the item is taxable |
| is_food_stampable | BOOLEAN | Whether the item is eligible for food stamps |
| is_wic_eligible | BOOLEAN | Whether the item is WIC eligible |
| is_age_restricted | BOOLEAN | Whether the item has age restrictions |
| minimum_age | SMALLINT | Minimum age required to purchase (if age-restricted) |
| is_returnable | BOOLEAN | Whether the item can be returned |
| is_scale_item | BOOLEAN | Whether the item is sold by weight |
| is_active | BOOLEAN | Whether the item is active in the system |
| item_image_url | TEXT | URL to the item's image |
| tare_weight | NUMERIC(10, 3) | Tare weight for scale items |
| cost | NUMERIC(10, 2) | Cost of the item |

### item_price

Stores pricing information for items, allowing for different prices by store and time period.

| Column | Type | Description |
|--------|------|-------------|
| item_price_id | SERIAL PRIMARY KEY | Unique price identifier |
| item_id | INTEGER REFERENCES item(item_id) | Foreign key to the item |
| price | NUMERIC(10, 2) | Regular price |
| start_date | TIMESTAMP | When the price becomes effective |
| end_date | TIMESTAMP | When the price expires (null if permanent) |
| store_id | INTEGER REFERENCES store(store_id) | Foreign key to the store |

### customer

Stores information about customers, including loyalty program members.

| Column | Type | Description |
|--------|------|-------------|
| customer_id | SERIAL PRIMARY KEY | Unique customer identifier |
| loyalty_card_number | VARCHAR(50) UNIQUE | Loyalty card number |
| first_name | VARCHAR(100) | First name |
| last_name | VARCHAR(100) | Last name |
| phone_number | VARCHAR(20) | Phone number |
| email | VARCHAR(255) | Email address |
| tax_exempt_id | TEXT | Tax exemption ID (if applicable) |
| date_of_birth | DATE | Date of birth |

### role

Defines roles for employees.

| Column | Type | Description |
|--------|------|-------------|
| role_id | SERIAL PRIMARY KEY | Unique role identifier |
| role_name | VARCHAR(50) | Name of the role (e.g., 'Cashier', 'Manager', 'Supervisor') |

### permission

Defines permissions that can be assigned to roles.

| Column | Type | Description |
|--------|------|-------------|
| permission_id | SERIAL PRIMARY KEY | Unique permission identifier |
| permission_name | TEXT | Name of the permission |
| permission_description | TEXT | Description of the permission |

### role_permission

Maps permissions to roles (many-to-many relationship).

| Column | Type | Description |
|--------|------|-------------|
| role_permission_id | SERIAL PRIMARY KEY | Unique role-permission mapping identifier |
| role_id | INTEGER REFERENCES role(role_id) | Foreign key to the role |
| permission_id | INTEGER REFERENCES permission(permission_id) | Foreign key to the permission |

### employee

Stores information about employees.

| Column | Type | Description |
|--------|------|-------------|
| employee_id | SERIAL PRIMARY KEY | Unique employee identifier |
| first_name | VARCHAR(100) | First name |
| last_name | VARCHAR(100) | Last name |
| login_id | VARCHAR(50) UNIQUE | Login ID for the system |
| password_hash | VARCHAR(255) | Hashed password |
| role_id | INTEGER REFERENCES role(role_id) | Foreign key to the role |
| store_id | INTEGER REFERENCES store(store_id) | Foreign key to the store |

## Transaction Tables

### transaction

Stores information about sales transactions.

| Column | Type | Description |
|--------|------|-------------|
| transaction_id | BIGSERIAL PRIMARY KEY | Unique transaction identifier |
| store_id | INTEGER REFERENCES store(store_id) | Foreign key to the store |
| lane_id | INTEGER REFERENCES lane(lane_id) | Foreign key to the lane |
| employee_id | INTEGER REFERENCES employee(employee_id) | Cashier who started the transaction |
| customer_id | INTEGER REFERENCES customer(customer_id) | Customer associated with the transaction |
| transaction_timestamp | TIMESTAMP | When the transaction occurred |
| transaction_type | VARCHAR(50) | Type of transaction (e.g., 'SALE', 'RETURN', 'VOID', 'SUSPEND') |
| total_amount | NUMERIC(10, 2) | Total amount of the transaction |
| original_transaction_id | BIGINT REFERENCES transaction(transaction_id) | For returns/voids, links to the original transaction |
| is_training | BOOLEAN | Whether this is a training transaction |

### transaction_line_item

Stores information about individual items in a transaction.

| Column | Type | Description |
|--------|------|-------------|
| transaction_line_item_id | BIGSERIAL PRIMARY KEY | Unique line item identifier |
| transaction_id | BIGINT REFERENCES transaction(transaction_id) | Foreign key to the transaction |
| item_id | INTEGER REFERENCES item(item_id) | Foreign key to the item |
| quantity | NUMERIC(10, 3) | Quantity (can be fractional for weight/volume items) |
| unit_price | NUMERIC(10, 2) | Price at the time of sale |
| discount_amount | NUMERIC(10, 2) | Total discount applied to this line item |
| tax_amount | NUMERIC(10, 2) | Total tax applied to this line item |
| extended_price | NUMERIC(10, 2) | (quantity * unit_price) - discount_amount |
| line_item_type | VARCHAR(50) | Type of line item (e.g., 'SALE', 'RETURN', 'VOID', 'COUPON') |
| serial_number | TEXT | Serial number (if applicable) |
| sequence_number | INT | Order of the item in the transaction |

### tax_rate

Stores information about tax rates.

| Column | Type | Description |
|--------|------|-------------|
| tax_rate_id | SERIAL PRIMARY KEY | Unique tax rate identifier |
| tax_rate_code | TEXT | Code for the tax rate |
| tax_rate | NUMERIC(5, 4) | Tax rate value |
| tax_juristiction | TEXT | Tax jurisdiction |
| start_date | TIMESTAMP | When the tax rate becomes effective |
| end_date | TIMESTAMP | When the tax rate expires (null if permanent) |
| store_id | INTEGER REFERENCES store(store_id) | Foreign key to the store |

### transaction_line_item_tax

Stores information about taxes applied to line items.

| Column | Type | Description |
|--------|------|-------------|
| transaction_line_item_tax_id | BIGSERIAL PRIMARY KEY | Unique line item tax identifier |
| transaction_line_item_id | BIGINT REFERENCES transaction_line_item(transaction_line_item_id) | Foreign key to the transaction_line_item |
| tax_rate_id | INTEGER REFERENCES tax_rate(tax_rate_id) | Foreign key to the tax_rate |
| taxable_amount | NUMERIC(10, 2) | Amount subject to tax |
| tax_amount | NUMERIC(10, 2) | Tax amount |

### transaction_tender

Stores information about payment methods used in a transaction.

| Column | Type | Description |
|--------|------|-------------|
| transaction_tender_id | BIGSERIAL PRIMARY KEY | Unique tender identifier |
| transaction_id | BIGINT REFERENCES transaction(transaction_id) | Foreign key to the transaction |
| tender_type | VARCHAR(50) | Type of tender (e.g., 'CASH', 'CREDIT_CARD', 'DEBIT_CARD', 'GIFT_CARD', 'CHECK', 'LOYALTY_POINTS', 'EBT') |
| amount | NUMERIC(10, 2) | Amount tendered |
| card_number | VARCHAR(255) | Encrypted card number (if applicable) |
| authorization_code | VARCHAR(50) | Authorization code from the payment processor |
| currency | VARCHAR(10) | Currency code |

## Promotion Tables

### promotion

Stores information about promotions.

| Column | Type | Description |
|--------|------|-------------|
| promotion_id | SERIAL PRIMARY KEY | Unique promotion identifier |
| promotion_name | VARCHAR(255) | Name of the promotion |
| promotion_type | VARCHAR(50) | Type of promotion (e.g., 'ITEM_DISCOUNT', 'TOTAL_DISCOUNT', 'BOGO', 'MIX_AND_MATCH') |
| start_date | TIMESTAMP | When the promotion becomes effective |
| end_date | TIMESTAMP | When the promotion expires (null if permanent) |
| is_active | BOOLEAN | Whether the promotion is active |
| discount_percentage | NUMERIC(5, 2) | Percentage discount (e.g., 10.00 for 10%) |
| discount_amount | NUMERIC(10, 2) | Fixed amount discount |
| minimum_quantity | INTEGER | Minimum quantity required for the promotion |
| maximum_quantity | INTEGER | Maximum quantity allowed for the promotion |
| priority | INTEGER | Priority for overlapping promotions (lower number = higher priority) |
| stackable | BOOLEAN | Whether this promotion can be combined with others |

### promotion_item

Maps items to promotions (many-to-many relationship).

| Column | Type | Description |
|--------|------|-------------|
| promotion_item_id | SERIAL PRIMARY KEY | Unique promotion-item mapping identifier |
| promotion_id | INTEGER REFERENCES promotion(promotion_id) | Foreign key to the promotion |
| item_id | INTEGER REFERENCES item(item_id) | Foreign key to the item |
| group_id | TEXT | Group ID for mix-and-match promotions |

## Indexes

The schema includes indexes for performance optimization:

- Indexes on foreign keys (e.g., `store_id`, `lane_id`, `employee_id`, `customer_id`)
- Indexes on frequently queried columns (e.g., `transaction_timestamp`, `upc`)
- Indexes for join operations (e.g., `transaction_id` in `transaction_line_item`)

## Usage

To create the database schema, run the SQL script:

```bash
psql -U your_username -d your_database -f database-schema.sql
```

The script will create all tables, indexes, and insert sample data for testing.

## TypeScript Integration

The schema is integrated with TypeScript through the `postgres.types.ts` file, which defines interfaces for all database tables and relationships. This enables type-safe database operations in the application.

The `postgresService.ts` file provides a comprehensive API for interacting with the database, including methods for retrieving, creating, and updating records.
