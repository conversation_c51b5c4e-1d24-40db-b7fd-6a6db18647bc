# TruPOS Quick Start Guide

Get up and running with <PERSON>ru<PERSON><PERSON> in minutes!

## Prerequisites

Before you begin, ensure you have:

- **Java 17+** (for backend) - [Download here](https://adoptium.net/)
- **Maven 3.6+** (for building) - [Download here](https://maven.apache.org/)
- **Git** - [Download here](https://git-scm.com/)
- **Code Editor** (VS Code recommended) - [Download here](https://code.visualstudio.com/)

## Quick Installation

### 1. Clone the Repository

```bash
git clone https://github.com/Chris-StClair/rico-suavi.git
cd trupos-backend-spring
```

### 2. Build the Backend

```bash
cd backend
mvn clean install
```

### 3. Start the System

**Option A: Use Startup Script (Recommended)**
```bash
./scripts/development/start-backend.sh
```

**Option B: Manual Startup**
```bash
cd backend
mvn spring-boot:run
```

### 4. Access the Application

- **Backend API**: http://localhost:8080/api
- **API Documentation**: http://localhost:8080/swagger-ui.html
- **Health Check**: http://localhost:8080/actuator/health

## Default API Access

The backend uses session-based authentication. For API testing:
- **Username**: `manager01`
- **Password**: `manager123`

## First Steps

### 1. Verify System Health

1. Open http://localhost:8080/actuator/health
2. Check that the response shows `{"status":"UP"}`
3. Open http://localhost:8080/swagger-ui.html
4. Test API endpoints using the interactive documentation

### 2. Test Basic API Operations

1. **Add Items**: Click on products or scan barcodes
2. **Quantity**: Press Quantity → 5 → Enter → Scan item
3. **Customer**: Click Customer Lookup → Add customer
4. **Payment**: Click Total → Select payment method
5. **Complete**: Process payment and print receipt

### 3. Explore Features

- **Product Management**: Add/edit products
- **Customer Management**: Create customer accounts
- **Reports**: View sales reports and analytics
- **Admin Functions**: Access manager menu

## Development Workflow

### File Structure

```
trupos-backend-spring/
├── backend/               # Spring Boot backend
│   ├── src/main/java/     # Java source code
│   │   └── com/trupos/    # Application packages
│   │       ├── controller/ # REST controllers
│   │       ├── service/   # Business logic
│   │       ├── repository/ # Data access
│   │       ├── entity/    # JPA entities
│   │       └── dto/       # Data transfer objects
│   ├── src/main/resources/ # Configuration files
│   ├── target/            # Build artifacts
│   └── pom.xml           # Maven configuration
├── database/              # Database scripts and setup
├── docs/                  # API documentation
├── scripts/               # Development and deployment scripts
└── tools/                 # Development tools
```

### Making Changes

1. **Backend Changes**: Edit Java files in `backend/src/main/java/`, restart service
2. **Database Changes**: Update schema files in `database/` or `backend/src/main/resources/`
3. **API Changes**: Update controllers and DTOs, regenerate API documentation

### Testing

```bash
# Backend tests
cd backend && mvn test

# Specific test class
cd backend && mvn test -Dtest=CustomerControllerTest

# API testing with Postman
# Import: tools/postman/TruPOS_Enhanced_API_Collection.postman_collection.json

# Test coverage
cd backend && mvn test jacoco:report
```

## Common API Tasks

### Add a New Product via API

```bash
# POST /api/products
curl -X POST http://localhost:8080/api/products \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Product",
    "price": 9.99,
    "upc": "123456789012",
    "departmentId": 1
  }'
```

### Create a Customer via API

```bash
# POST /api/customers
curl -X POST http://localhost:8080/api/customers \
  -H "Content-Type: application/json" \
  -d '{
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "phone": "555-1234"
  }'
```

### Process a Transaction via API

```bash
# POST /api/transactions
curl -X POST http://localhost:8080/api/transactions \
  -H "Content-Type: application/json" \
  -d '{
    "customerId": 1,
    "employeeId": 1,
    "storeId": 1,
    "laneId": 1
  }'
```

## Troubleshooting

### Backend Won't Start

```bash
# Check Java version
java -version

# Rebuild backend
cd backend && mvn clean package

# Check logs
tail -f backend/backend.log

# Check if port is in use
lsof -i :8080
```

### Database Issues

```bash
# Check database connection (H2 Console)
# http://localhost:8080/h2-console

# Reset database (development)
cd backend && mvn spring-boot:run -Dspring.jpa.hibernate.ddl-auto=create-drop

# Check PostgreSQL connection
cd backend && docker-compose up postgres
```

### API Issues

```bash
# Test API health
curl http://localhost:8080/actuator/health

# Check API documentation
# http://localhost:8080/swagger-ui.html

# Test specific endpoint
curl http://localhost:8080/api/customers
```

## Next Steps

1. **Read Documentation**: Explore [docs/](../README.md) for detailed API guides
2. **API Testing**: Use [Postman collections](../../tools/postman/) for comprehensive API testing
3. **Database Setup**: Configure PostgreSQL for production use
4. **Deployment**: Follow [deployment guide](../deployment/installation.md) for production setup
5. **Frontend Integration**: Connect your frontend application to the API

## Getting Help

- **API Documentation**: Interactive docs at http://localhost:8080/swagger-ui.html
- **Backend Documentation**: Check [backend/README.md](../../backend/README.md)
- **Database Documentation**: Check [database/README.md](../../database/README.md)
- **Postman Collections**: Use API examples in [tools/postman/](../../tools/postman/)

## Development Tips

1. **Use Swagger UI**: Test API endpoints interactively
2. **Monitor Logs**: Check backend logs for errors and debugging
3. **Test Early**: Use Postman to validate API changes
4. **Follow REST Patterns**: Study existing controllers for consistency
5. **Update Documentation**: Keep API docs current when adding endpoints
6. **Use Profiles**: Switch between H2 (dev) and PostgreSQL (prod) profiles

Happy coding! 🚀
