# TruPOS Development Tools

This directory contains development tools, testing utilities, and external integrations.

## Directory Structure

```
tools/
├── postman/            # API testing collections
│   ├── TruPOS_Enhanced_API_Collection.postman_collection.json
│   ├── TruPOS_API_Collection.json
│   └── environments/   # Postman environment files
├── testing/            # Testing utilities
│   ├── api/           # API testing tools
│   ├── ui/            # UI testing tools
│   └── performance/   # Performance testing
├── database/          # Database tools
│   ├── seeders/       # Data seeding scripts
│   ├── migrations/    # Database migration tools
│   └── backup/        # Backup utilities
└── monitoring/        # System monitoring tools
    ├── health-checks/ # Health check scripts
    ├── logs/          # Log analysis tools
    └── metrics/       # Performance metrics
```

## Postman Collections

### TruPOS Enhanced API Collection
- **File**: `postman/TruPOS_Enhanced_API_Collection.postman_collection.json`
- **Description**: Comprehensive API testing collection with business workflows
- **Features**:
  - Complete transaction flows
  - Customer management
  - Product operations
  - Authentication testing
  - Error handling scenarios

### TruPOS API Collection
- **File**: `postman/TruPOS_API_Collection.json`
- **Description**: Basic API endpoint testing
- **Features**:
  - Individual endpoint tests
  - Basic CRUD operations
  - Authentication flows

## Usage

### Importing Postman Collections

1. **Open Postman**
2. **Click Import**
3. **Select Collection File**:
   - `tools/postman/TruPOS_Enhanced_API_Collection.postman_collection.json`
4. **Configure Environment Variables**:
   - `base_url`: Your API base URL (e.g., `http://localhost:8080`)
   - `auth_token`: Authentication token (if required)

### Environment Setup

Create a Postman environment with these variables:
```json
{
  "base_url": "http://localhost:8080",
  "api_url": "http://localhost:8080/api",
  "auth_token": "",
  "store_id": "1",
  "lane_id": "1",
  "employee_id": "1"
}
```

### Running Collections

1. **Individual Requests**: Click on any request and hit Send
2. **Collection Runner**: Use Postman's Collection Runner for automated testing
3. **Newman CLI**: Run collections from command line:
   ```bash
   newman run tools/postman/TruPOS_Enhanced_API_Collection.postman_collection.json
   ```

## Testing Workflows

### Complete Transaction Flow
1. Create Transaction
2. Add Line Items
3. Add Customer (optional)
4. Process Payment
5. Complete Transaction

### Customer Management
1. Create Customer
2. Search Customers
3. Update Customer
4. Loyalty Operations

### Product Operations
1. Search Products
2. Get Product Details
3. Check Inventory
4. Price Inquiries

## API Documentation

The Postman collections serve as interactive API documentation. Each request includes:
- **Description**: What the endpoint does
- **Parameters**: Required and optional parameters
- **Examples**: Sample requests and responses
- **Tests**: Automated validation scripts

## Development Workflow

1. **Start Backend**: `./scripts/development/start-trupos-backend.sh`
2. **Import Collection**: Load Postman collection
3. **Set Environment**: Configure base URLs and credentials
4. **Test Endpoints**: Validate API functionality
5. **Document Changes**: Update collections when API changes

## Support

For issues with API testing:
1. Check endpoint documentation in Postman
2. Verify environment variables
3. Confirm backend is running
4. Review API logs for errors
5. Contact development team if needed
