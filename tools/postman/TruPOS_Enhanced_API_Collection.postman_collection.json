{"info": {"name": "TruPOS Enhanced API Collection", "description": "Comprehensive collection of TruPOS API endpoints for real-world POS operations", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "version": "2.0.0"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8080/api", "type": "string"}, {"key": "storeId", "value": "1", "type": "string"}, {"key": "employeeId", "value": "1", "type": "string"}, {"key": "laneId", "value": "1", "type": "string"}, {"key": "transactionId", "value": "", "type": "string"}, {"key": "customerId", "value": "", "type": "string"}, {"key": "productId", "value": "", "type": "string"}], "item": [{"name": "🏪 Store Setup & Management", "item": [{"name": "Create Sample Store", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"storeName\": \"TruPOS Demo Store\",\n  \"storeNo\": 1001,\n  \"address\": \"123 Main Street\",\n  \"city\": \"Anytown\",\n  \"state\": \"CA\",\n  \"zipCode\": \"90210\",\n  \"phoneNumber\": \"************\",\n  \"taxRate\": 0.0875,\n  \"wicStateId\": 12345\n}"}, "url": {"raw": "{{baseUrl}}/stores", "host": ["{{baseUrl}}"], "path": ["stores"]}, "description": "Create a sample store for testing"}}]}, {"name": "👥 Employee Management", "item": [{"name": "Create Manager Employee", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"loginId\": \"manager01\",\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"Manager\",\n  \"email\": \"<EMAIL>\",\n  \"isActive\": true,\n  \"storeId\": {{storeId}}\n}"}, "url": {"raw": "{{baseUrl}}/employees?password=manager123", "host": ["{{baseUrl}}"], "path": ["employees"], "query": [{"key": "password", "value": "manager123"}]}, "description": "Create a manager employee for testing"}}, {"name": "Create Cashier Employee", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"loginId\": \"cashier01\",\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"Cashier\",\n  \"email\": \"<EMAIL>\",\n  \"isActive\": true,\n  \"storeId\": {{storeId}}\n}"}, "url": {"raw": "{{baseUrl}}/employees?password=cashier123", "host": ["{{baseUrl}}"], "path": ["employees"], "query": [{"key": "password", "value": "cashier123"}]}, "description": "Create a cashier employee for testing"}}, {"name": "Get All Active Employees", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/employees/active", "host": ["{{baseUrl}}"], "path": ["employees", "active"]}, "description": "Get all active employees in the system"}}, {"name": "Get Employees by Store", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/employees/store/{{storeId}}", "host": ["{{baseUrl}}"], "path": ["employees", "store", "{{storeId}}"]}, "description": "Get all employees for a specific store"}}, {"name": "Authenticate Employee", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"loginId\": \"manager01\",\n  \"password\": \"manager123\"\n}"}, "url": {"raw": "{{baseUrl}}/employees/authenticate", "host": ["{{baseUrl}}"], "path": ["employees", "authenticate"]}, "description": "Authenticate an employee login"}}]}, {"name": "🛍️ Product & Inventory Management", "item": [{"name": "🔍 Product Discovery", "item": [{"name": "Get All Active Products (Sellable Items)", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products/active", "host": ["{{baseUrl}}"], "path": ["products", "active"]}, "description": "Get all products that can be sold (active items)"}}, {"name": "Search Products by Name/Description", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products/search?q=apple&active=true&page=0&size=10", "host": ["{{baseUrl}}"], "path": ["products", "search"], "query": [{"key": "q", "value": "apple", "description": "Search term"}, {"key": "active", "value": "true", "description": "Only active products"}, {"key": "page", "value": "0"}, {"key": "size", "value": "10"}]}, "description": "Search for products by name or description"}}, {"name": "Find Product by UPC/Barcode", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products/upc/**********12", "host": ["{{baseUrl}}"], "path": ["products", "upc", "**********12"]}, "description": "Find a product by scanning its barcode/UPC"}}, {"name": "Find Product by SKU", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products/sku/APPLE-001", "host": ["{{baseUrl}}"], "path": ["products", "sku", "APPLE-001"]}, "description": "Find a product by its SKU"}}]}, {"name": "💰 Pricing", "item": [{"name": "Get Current Price for Product", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products/1/price?storeId={{storeId}}", "host": ["{{baseUrl}}"], "path": ["products", "1", "price"], "query": [{"key": "storeId", "value": "{{storeId}}"}]}, "description": "Get the current selling price for a product at a specific store"}}]}, {"name": "🏷️ Special Categories", "item": [{"name": "Get Scale Items (Weighed Products)", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products/scale-items", "host": ["{{baseUrl}}"], "path": ["products", "scale-items"]}, "description": "Get all products that need to be weighed (produce, deli, etc.)"}}, {"name": "Get Age Restricted Items", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products/age-restricted", "host": ["{{baseUrl}}"], "path": ["products", "age-restricted"]}, "description": "Get all products that require age verification (alcohol, tobacco, etc.)"}}, {"name": "Get WIC Eligible Items", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products/wic-eligible", "host": ["{{baseUrl}}"], "path": ["products", "wic-eligible"]}, "description": "Get all products eligible for WIC (Women, Infants, and Children) program"}}, {"name": "Check Age Verification Required", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products/1/age-verification", "host": ["{{baseUrl}}"], "path": ["products", "1", "age-verification"]}, "description": "Check if a specific product requires age verification"}}, {"name": "Get Minimum Age for Product", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products/1/minimum-age", "host": ["{{baseUrl}}"], "path": ["products", "1", "minimum-age"]}, "description": "Get the minimum age required to purchase a product"}}]}, {"name": "📦 Product Management", "item": [{"name": "Create New Product", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"description\": \"Red Delicious Apples\",\n  \"upc\": \"**********12\",\n  \"sku\": \"APPLE-001\",\n  \"brand\": \"Fresh Farms\",\n  \"size\": \"1 lb bag\",\n  \"cost\": 1.50,\n  \"isActive\": true,\n  \"isTaxable\": false,\n  \"isScaleItem\": false,\n  \"isAgeRestricted\": false,\n  \"isWicEligible\": true,\n  \"isFoodStampable\": true,\n  \"isReturnable\": true,\n  \"unitOfMeasure\": \"EA\",\n  \"departmentId\": 1\n}"}, "url": {"raw": "{{baseUrl}}/products", "host": ["{{baseUrl}}"], "path": ["products"]}, "description": "Create a new product in the system"}}, {"name": "Update Product", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"description\": \"Red Delicious Apples - Premium\",\n  \"cost\": 1.75,\n  \"isActive\": true\n}"}, "url": {"raw": "{{baseUrl}}/products/1", "host": ["{{baseUrl}}"], "path": ["products", "1"]}, "description": "Update an existing product"}}, {"name": "Activate Product", "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/products/1/activate", "host": ["{{baseUrl}}"], "path": ["products", "1", "activate"]}, "description": "Activate a product for sale"}}, {"name": "Deactivate Product", "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/products/1/deactivate", "host": ["{{baseUrl}}"], "path": ["products", "1", "deactivate"]}, "description": "Deactivate a product (remove from sale)"}}]}]}, {"name": "👤 Customer Management", "item": [{"name": "🔍 Customer Lookup", "item": [{"name": "Search Customers", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/customers/search?q=john&page=0&size=10", "host": ["{{baseUrl}}"], "path": ["customers", "search"], "query": [{"key": "q", "value": "john", "description": "Search by name, email, or phone"}, {"key": "page", "value": "0"}, {"key": "size", "value": "10"}]}, "description": "Search for customers by name, email, or phone number"}}, {"name": "Find Customer by Loyalty Card", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/customers/loyalty/**********", "host": ["{{baseUrl}}"], "path": ["customers", "loyalty", "**********"]}, "description": "Find customer by scanning their loyalty card"}}, {"name": "Find Customer by Phone", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/customers/phone/************", "host": ["{{baseUrl}}"], "path": ["customers", "phone", "************"]}, "description": "Find customers by phone number"}}, {"name": "Find Customer by Email", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/customers/email/<EMAIL>", "host": ["{{baseUrl}}"], "path": ["customers", "email", "<EMAIL>"]}, "description": "Find customer by email address"}}, {"name": "Check if Customer is Tax Exempt", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/customers/1/tax-exempt", "host": ["{{baseUrl}}"], "path": ["customers", "1", "tax-exempt"]}, "description": "Check if a customer is tax exempt"}}]}, {"name": "👤 Customer Management", "item": [{"name": "Create New Customer", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"Doe\",\n  \"email\": \"<EMAIL>\",\n  \"phoneNumber\": \"************\",\n  \"addressLine1\": \"123 Main St\",\n  \"city\": \"Anytown\",\n  \"state\": \"CA\",\n  \"postalCode\": \"90210\",\n  \"country\": \"USA\",\n  \"loyaltyCardNumber\": \"**********\",\n  \"isActive\": true\n}"}, "url": {"raw": "{{baseUrl}}/customers", "host": ["{{baseUrl}}"], "path": ["customers"]}, "description": "Create a new customer account"}}, {"name": "Get Customer Count", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/customers/count", "host": ["{{baseUrl}}"], "path": ["customers", "count"]}, "description": "Get total number of active customers"}}]}]}, {"name": "🛒 Transaction Processing", "item": [{"name": "💳 POS Operations", "item": [{"name": "Start New Transaction", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('transactionId', response.transactionId);", "    console.log('Transaction created with ID:', response.transactionId);", "}"]}}], "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/transactions?storeId={{storeId}}&laneId={{laneId}}&employeeId={{employeeId}}", "host": ["{{baseUrl}}"], "path": ["transactions"], "query": [{"key": "storeId", "value": "{{storeId}}"}, {"key": "laneId", "value": "{{laneId}}"}, {"key": "employeeId", "value": "{{employeeId}}"}]}, "description": "Start a new transaction at the POS"}}, {"name": "Start Transaction with Customer", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/transactions?storeId={{storeId}}&laneId={{laneId}}&employeeId={{employeeId}}&customerId=1", "host": ["{{baseUrl}}"], "path": ["transactions"], "query": [{"key": "storeId", "value": "{{storeId}}"}, {"key": "laneId", "value": "{{laneId}}"}, {"key": "employeeId", "value": "{{employeeId}}"}, {"key": "customerId", "value": "1"}]}, "description": "Start a new transaction with a customer attached"}}, {"name": "Add Item to Transaction", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/transactions/{{transactionId}}/line-items?itemId={{productId}}&quantity=1&unitPrice=1.99", "host": ["{{baseUrl}}"], "path": ["transactions", "{{transactionId}}", "line-items"], "query": [{"key": "itemId", "value": "{{productId}}"}, {"key": "quantity", "value": "1"}, {"key": "unitPrice", "value": "1.99"}]}, "description": "Add an item to an existing transaction"}}, {"name": "Add Customer to Transaction", "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/transactions/{{transactionId}}/customer?customerId={{customerId}}", "host": ["{{baseUrl}}"], "path": ["transactions", "{{transactionId}}", "customer"], "query": [{"key": "customerId", "value": "{{customerId}}"}]}, "description": "Add or update customer on an existing transaction"}}, {"name": "Add Cash Tender", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/transactions/{{transactionId}}/tenders?tenderType=CASH&amount=10.00", "host": ["{{baseUrl}}"], "path": ["transactions", "{{transactionId}}", "tenders"], "query": [{"key": "tenderType", "value": "CASH"}, {"key": "amount", "value": "10.00"}]}, "description": "Add cash payment to transaction"}}, {"name": "Add Card Tender", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/transactions/{{transactionId}}/tenders?tenderType=CREDIT_CARD&amount=25.50&cardLastFour=1234&authorizationCode=AUTH123", "host": ["{{baseUrl}}"], "path": ["transactions", "{{transactionId}}", "tenders"], "query": [{"key": "tenderType", "value": "CREDIT_CARD"}, {"key": "amount", "value": "25.50"}, {"key": "cardLastFour", "value": "1234"}, {"key": "authorizationCode", "value": "AUTH123"}]}, "description": "Add credit card payment to transaction"}}, {"name": "Complete Transaction", "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/transactions/{{transactionId}}/complete", "host": ["{{baseUrl}}"], "path": ["transactions", "{{transactionId}}", "complete"]}, "description": "Complete and finalize a transaction"}}, {"name": "Suspend Transaction", "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/transactions/1/suspend", "host": ["{{baseUrl}}"], "path": ["transactions", "1", "suspend"]}, "description": "Suspend a transaction to resume later"}}, {"name": "Resume Suspended Transaction", "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/transactions/1/resume", "host": ["{{baseUrl}}"], "path": ["transactions", "1", "resume"]}, "description": "Resume a previously suspended transaction"}}, {"name": "Void Transaction", "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/transactions/1/void?voidReason=Customer%20request", "host": ["{{baseUrl}}"], "path": ["transactions", "1", "void"], "query": [{"key": "voidReason", "value": "Customer request"}]}, "description": "Void a transaction with a reason"}}]}, {"name": "📊 Transaction Reporting", "item": [{"name": "Get Transactions by Store", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/transactions/store/{{storeId}}?page=0&size=20&sortBy=transactionTimestamp&sortDir=desc", "host": ["{{baseUrl}}"], "path": ["transactions", "store", "{{storeId}}"], "query": [{"key": "page", "value": "0"}, {"key": "size", "value": "20"}, {"key": "sortBy", "value": "transactionTimestamp"}, {"key": "sortDir", "value": "desc"}]}, "description": "Get recent transactions for a store"}}, {"name": "Get Suspended Transactions by Lane", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/transactions/suspended/lane/{{laneId}}", "host": ["{{baseUrl}}"], "path": ["transactions", "suspended", "lane", "{{laneId}}"]}, "description": "Get all suspended transactions for a specific lane"}}, {"name": "Calculate Total Sales for Date Range", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/transactions/sales-total?storeId={{storeId}}&startDate=2024-01-01T00:00:00&endDate=2024-12-31T23:59:59", "host": ["{{baseUrl}}"], "path": ["transactions", "sales-total"], "query": [{"key": "storeId", "value": "{{storeId}}"}, {"key": "startDate", "value": "2024-01-01T00:00:00"}, {"key": "endDate", "value": "2024-12-31T23:59:59"}]}, "description": "Calculate total sales for a date range"}}, {"name": "Get Transaction by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/transactions/1", "host": ["{{baseUrl}}"], "path": ["transactions", "1"]}, "description": "Get detailed information about a specific transaction"}}]}]}, {"name": "🔄 Complete Workflow Scenarios", "item": [{"name": "📋 Normal Transaction Flow", "item": [{"name": "1. Create Transaction", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('transactionId', response.transactionId);", "    console.log('✅ Transaction created with ID:', response.transactionId);", "} else {", "    console.log('❌ Failed to create transaction');", "}"]}}], "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/transactions?storeId={{storeId}}&laneId={{laneId}}&employeeId={{employeeId}}", "host": ["{{baseUrl}}"], "path": ["transactions"], "query": [{"key": "storeId", "value": "{{storeId}}"}, {"key": "laneId", "value": "{{laneId}}"}, {"key": "employeeId", "value": "{{employeeId}}"}]}, "description": "Step 1: Start a new transaction"}}, {"name": "2. Add First Item", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    console.log('✅ First item added to transaction');", "} else {", "    console.log('❌ Failed to add first item');", "}"]}}], "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/transactions/{{transactionId}}/line-items?itemId=1&quantity=2&unitPrice=1.99", "host": ["{{baseUrl}}"], "path": ["transactions", "{{transactionId}}", "line-items"], "query": [{"key": "itemId", "value": "1"}, {"key": "quantity", "value": "2"}, {"key": "unitPrice", "value": "1.99"}]}, "description": "Step 2: Add first item (2x $1.99 items)"}}, {"name": "3. Add Second Item", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    console.log('✅ Second item added to transaction');", "} else {", "    console.log('❌ Failed to add second item');", "}"]}}], "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/transactions/{{transactionId}}/line-items?itemId=2&quantity=1&unitPrice=3.49", "host": ["{{baseUrl}}"], "path": ["transactions", "{{transactionId}}", "line-items"], "query": [{"key": "itemId", "value": "2"}, {"key": "quantity", "value": "1"}, {"key": "unitPrice", "value": "3.49"}]}, "description": "Step 3: Add second item (1x $3.49 item)"}}, {"name": "4. Search and Add Customer", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.content && response.content.length > 0) {", "        pm.collectionVariables.set('customerId', response.content[0].customerId);", "        console.log('✅ Customer found with ID:', response.content[0].customerId);", "    } else {", "        console.log('⚠️ No customers found in search');", "    }", "} else {", "    console.log('❌ Failed to search customers');", "}"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/customers/search?q=john&page=0&size=10", "host": ["{{baseUrl}}"], "path": ["customers", "search"], "query": [{"key": "q", "value": "john"}, {"key": "page", "value": "0"}, {"key": "size", "value": "10"}]}, "description": "Step 4a: Search for customer"}}, {"name": "5. Add Customer to Transaction", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    console.log('✅ Customer added to transaction');", "} else {", "    console.log('❌ Failed to add customer to transaction');", "}"]}}], "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/transactions/{{transactionId}}/customer?customerId={{customerId}}", "host": ["{{baseUrl}}"], "path": ["transactions", "{{transactionId}}", "customer"], "query": [{"key": "customerId", "value": "{{customerId}}"}]}, "description": "Step 4b: Add customer to transaction"}}, {"name": "6. <PERSON><PERSON> <PERSON>der", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    console.log('✅ Cash tender added to transaction');", "} else {", "    console.log('❌ Failed to add cash tender');", "}"]}}], "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/transactions/{{transactionId}}/tenders?tenderType=CASH&amount=10.00", "host": ["{{baseUrl}}"], "path": ["transactions", "{{transactionId}}", "tenders"], "query": [{"key": "tenderType", "value": "CASH"}, {"key": "amount", "value": "10.00"}]}, "description": "Step 5: Add cash payment ($10.00)"}}, {"name": "7. Complete Transaction", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    console.log('✅ Transaction completed successfully!');", "    console.log('Transaction ID:', response.transactionId);", "    console.log('Total Amount:', response.totalAmount);", "    console.log('Status:', response.status);", "} else {", "    console.log('❌ Failed to complete transaction');", "}"]}}], "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/transactions/{{transactionId}}/complete", "host": ["{{baseUrl}}"], "path": ["transactions", "{{transactionId}}", "complete"]}, "description": "Step 6: Complete and finalize the transaction"}}]}, {"name": "🛒 Product Lookup Scenarios", "item": [{"name": "Find Product by UPC/Barcode", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('productId', response.itemId);", "    console.log('✅ Product found by UPC:', response.description);", "    console.log('Product ID:', response.itemId);", "    console.log('Price: $' + response.cost);", "} else {", "    console.log('❌ Product not found by UPC');", "}"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products/upc/111111111111", "host": ["{{baseUrl}}"], "path": ["products", "upc", "111111111111"]}, "description": "Look up product by scanning barcode/UPC"}}, {"name": "Search Products by Name", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.content && response.content.length > 0) {", "        pm.collectionVariables.set('productId', response.content[0].itemId);", "        console.log('✅ Products found by search:', response.content.length);", "        console.log('First product:', response.content[0].description);", "    } else {", "        console.log('⚠️ No products found in search');", "    }", "} else {", "    console.log('❌ Failed to search products');", "}"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products/search?q=banana&active=true&page=0&size=10", "host": ["{{baseUrl}}"], "path": ["products", "search"], "query": [{"key": "q", "value": "banana"}, {"key": "active", "value": "true"}, {"key": "page", "value": "0"}, {"key": "size", "value": "10"}]}, "description": "Search for products by name or description"}}]}]}, {"name": "🔧 System Administration", "item": [{"name": "📈 Analytics & Reports", "item": [{"name": "Get Customer Count", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/customers/count", "host": ["{{baseUrl}}"], "path": ["customers", "count"]}, "description": "Get total number of active customers"}}, {"name": "Get All Managers", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/employees/managers", "host": ["{{baseUrl}}"], "path": ["employees", "managers"]}, "description": "Get all employees with manager privileges"}}, {"name": "Search Employees", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/employees/search?q=john", "host": ["{{baseUrl}}"], "path": ["employees", "search"], "query": [{"key": "q", "value": "john"}]}, "description": "Search employees by name"}}]}, {"name": "🏪 Store Operations", "item": [{"name": "Get Products by Department", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products/department/1", "host": ["{{baseUrl}}"], "path": ["products", "department", "1"]}, "description": "Get all products in a specific department"}}, {"name": "Get Active Employees by Store", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/employees/active/store/{{storeId}}", "host": ["{{baseUrl}}"], "path": ["employees", "active", "store", "{{storeId}}"]}, "description": "Get all active employees for a specific store"}}]}]}, {"name": "🧪 Testing & Development", "item": [{"name": "Create Sample Data", "item": [{"name": "Create Sample Product - Banana", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"description\": \"Fresh Bananas\",\n  \"upc\": \"111111111111\",\n  \"sku\": \"BANANA-001\",\n  \"brand\": \"Fresh Farms\",\n  \"size\": \"1 lb\",\n  \"cost\": 0.75,\n  \"isActive\": true,\n  \"isTaxable\": false,\n  \"isScaleItem\": true,\n  \"isAgeRestricted\": false,\n  \"isWicEligible\": true,\n  \"isFoodStampable\": true,\n  \"isReturnable\": true,\n  \"unitOfMeasure\": \"LB\",\n  \"departmentId\": 1\n}"}, "url": {"raw": "{{baseUrl}}/products", "host": ["{{baseUrl}}"], "path": ["products"]}, "description": "Create a sample scale item (bananas)"}}, {"name": "Create Sample Product - Beer", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"description\": \"Premium Beer 6-Pack\",\n  \"upc\": \"222222222222\",\n  \"sku\": \"BEER-001\",\n  \"brand\": \"Premium Brewery\",\n  \"size\": \"6 x 12oz\",\n  \"cost\": 6.99,\n  \"isActive\": true,\n  \"isTaxable\": true,\n  \"isScaleItem\": false,\n  \"isAgeRestricted\": true,\n  \"minimumAge\": 21,\n  \"isWicEligible\": false,\n  \"isFoodStampable\": false,\n  \"isReturnable\": true,\n  \"unitOfMeasure\": \"EA\",\n  \"departmentId\": 2\n}"}, "url": {"raw": "{{baseUrl}}/products", "host": ["{{baseUrl}}"], "path": ["products"]}, "description": "Create a sample age-restricted item (beer)"}}]}]}]}